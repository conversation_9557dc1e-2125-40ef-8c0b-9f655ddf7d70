// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:flutter_secure_storage/flutter_secure_storage.dart' as _i558;
import 'package:frontend/core/config/di/core/core_module.dart' as _i320;
import 'package:frontend/core/config/di/core/external_module.dart' as _i213;
import 'package:frontend/core/config/di/features/inventory_module.dart' as _i40;
import 'package:frontend/core/config/di/features/order_module.dart' as _i503;
import 'package:frontend/core/errors/http_error_handler.dart' as _i512;
import 'package:frontend/core/network/api_client/dio_api_client.dart' as _i257;
import 'package:frontend/core/network/connection_checker.dart' as _i310;
import 'package:frontend/core/services/notification_services.dart' as _i169;
import 'package:frontend/features/authentication/data/source/local/authentication_local_data_source.dart'
    as _i1037;
import 'package:frontend/features/authentication/data/source/remote/authentication_remote_data_source.dart'
    as _i322;
import 'package:frontend/features/authentication/domain/repository/authentication_repository.dart'
    as _i555;
import 'package:frontend/features/authentication/domain/usecase/check_authentication_usecase.dart'
    as _i1001;
import 'package:frontend/features/authentication/domain/usecase/complete_onboarding_use_case.dart'
    as _i573;
import 'package:frontend/features/authentication/domain/usecase/login_usecase.dart'
    as _i247;
import 'package:frontend/features/authentication/domain/usecase/logout_usecase.dart'
    as _i67;
import 'package:frontend/features/authentication/domain/usecase/resend_otp_use_case.dart'
    as _i38;
import 'package:frontend/features/authentication/domain/usecase/verify_otp_use_case.dart'
    as _i200;
import 'package:frontend/features/dashboard/data/source/remote/dashboard_remote_datasource.dart'
    as _i148;
import 'package:frontend/features/dashboard/domain/repositories/dashboard_reposiory.dart'
    as _i541;
import 'package:frontend/features/dashboard/domain/usecases/get_admin_dashboard_report.dart'
    as _i249;
import 'package:frontend/features/dashboard/domain/usecases/get_admin_dashboard_usecase.dart'
    as _i383;
import 'package:frontend/features/dashboard/domain/usecases/get_agent_dashboard_usecase.dart'
    as _i500;
import 'package:frontend/features/dashboard/domain/usecases/get_customer_dashboard_usecase.dart'
    as _i239;
import 'package:frontend/features/inventory/data/source/remote/inventory_remote_datasource.dart'
    as _i369;
import 'package:frontend/features/inventory/domain/repository/inventory_repository.dart'
    as _i429;
import 'package:frontend/features/inventory/domain/usecases/adjust_cylinder_stock_usecase.dart'
    as _i990;
import 'package:frontend/features/inventory/domain/usecases/adjust_package_stock_usecase.dart'
    as _i799;
import 'package:frontend/features/inventory/domain/usecases/adjust_spare_part_stock_usecase.dart'
    as _i132;
import 'package:frontend/features/inventory/domain/usecases/bulk_update_cylinder_status_usecase.dart'
    as _i796;
import 'package:frontend/features/inventory/domain/usecases/bulk_update_package_status_usecase.dart'
    as _i3;
import 'package:frontend/features/inventory/domain/usecases/bulk_update_spare_part_status_usecase.dart'
    as _i597;
import 'package:frontend/features/inventory/domain/usecases/create_cylinder_usecase.dart'
    as _i237;
import 'package:frontend/features/inventory/domain/usecases/create_package_usecase.dart'
    as _i437;
import 'package:frontend/features/inventory/domain/usecases/create_spare_part_usecase.dart'
    as _i892;
import 'package:frontend/features/inventory/domain/usecases/delete_cylinder_usecase.dart'
    as _i750;
import 'package:frontend/features/inventory/domain/usecases/delete_package_usecase.dart'
    as _i184;
import 'package:frontend/features/inventory/domain/usecases/delete_spare_part_usecase.dart'
    as _i844;
import 'package:frontend/features/inventory/domain/usecases/get_cylinder_by_id_usecase.dart'
    as _i810;
import 'package:frontend/features/inventory/domain/usecases/get_cylinders_usecase.dart'
    as _i100;
import 'package:frontend/features/inventory/domain/usecases/get_package_by_id_usecase.dart'
    as _i967;
import 'package:frontend/features/inventory/domain/usecases/get_packages_usecase.dart'
    as _i435;
import 'package:frontend/features/inventory/domain/usecases/get_spare_part_by_id_usecase.dart'
    as _i672;
import 'package:frontend/features/inventory/domain/usecases/get_spare_parts_usecase.dart'
    as _i472;
import 'package:frontend/features/inventory/domain/usecases/restock_cylinder_usecase.dart'
    as _i108;
import 'package:frontend/features/inventory/domain/usecases/restock_package_usecase.dart'
    as _i853;
import 'package:frontend/features/inventory/domain/usecases/restock_spare_part_usecase.dart'
    as _i644;
import 'package:frontend/features/inventory/domain/usecases/update_cylinder_usecase.dart'
    as _i118;
import 'package:frontend/features/inventory/domain/usecases/update_package_usecase.dart'
    as _i670;
import 'package:frontend/features/inventory/domain/usecases/update_spare_part_usecase.dart'
    as _i954;
import 'package:frontend/features/inventory/presentation/bloc/inventory_bloc.dart'
    as _i65;
import 'package:frontend/features/order/data/source/order_remote_datasource.dart'
    as _i169;
import 'package:frontend/features/order/domain/repositories/order_repository.dart'
    as _i574;
import 'package:frontend/features/order/domain/usecases/assign_agent_to_order_usecase.dart'
    as _i34;
import 'package:frontend/features/order/domain/usecases/cancel_order_usecase.dart'
    as _i1069;
import 'package:frontend/features/order/domain/usecases/complete_order_usecase.dart'
    as _i795;
import 'package:frontend/features/order/domain/usecases/create_order_usecase.dart'
    as _i998;
import 'package:frontend/features/order/domain/usecases/delete_order_usecase.dart'
    as _i386;
import 'package:frontend/features/order/domain/usecases/get_orders_usecase.dart'
    as _i387;
import 'package:frontend/features/order/domain/usecases/update_order_usecase.dart'
    as _i715;
import 'package:frontend/features/order/domain/usecases/validate_order_qr_usecase.dart'
    as _i729;
import 'package:frontend/features/order/presentation/bloc/order_bloc.dart'
    as _i900;
import 'package:frontend/features/shared/data/source/local/flutter_secure_storage_services.dart'
    as _i522;
import 'package:frontend/features/shared/presentation/bloc/bottom-nav-cubit/bottom_nav_cubit.dart'
    as _i823;
import 'package:frontend/features/shared/presentation/bloc/theme-cubit/theme_cubit.dart'
    as _i269;
import 'package:frontend/features/shared/presentation/bloc/theme-cubit/theme_storage.dart'
    as _i518;
import 'package:frontend/features/user-management/data/source/remote/user_remote_datasource.dart'
    as _i1005;
import 'package:frontend/features/user-management/domain/repository/user_repository.dart'
    as _i550;
import 'package:frontend/features/user-management/domain/usecase/delete_user_usecase.dart'
    as _i63;
import 'package:frontend/features/user-management/domain/usecase/get_all_users_usecase.dart'
    as _i87;
import 'package:frontend/features/user-management/domain/usecase/get_current_user_usecase.dart'
    as _i968;
import 'package:frontend/features/user-management/domain/usecase/register_admin_or_agent_usecase.dart'
    as _i973;
import 'package:frontend/features/user-management/domain/usecase/subscribe_to_notifications_usecase.dart'
    as _i25;
import 'package:frontend/features/user-management/domain/usecase/unsubscribe_from_notifications_usecase.dart'
    as _i874;
import 'package:frontend/features/user-management/domain/usecase/update_user_usecase.dart'
    as _i234;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart'
    as _i161;

import '../../../features/authentication/presentation/bloc/authentication_bloc.dart';
import '../../../features/dashboard/presentation/bloc/dashboard_bloc.dart';
import '../../../features/user-management/presentation/bloc/user_bloc.dart';
import 'features/authentication_module.dart';
import 'features/dashboard_module.dart';
import 'features/user_module.dart';

extension GetItInjectableX on _i174.GetIt {
  // initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(this, environment, environmentFilter);
    final externalModule = _$ExternalModule();
    final coreModule = _$CoreModule();
    final authenticationModule = _$AuthenticationModule();
    final orderModule = _$OrderModule();
    final dashboardModule = _$DashboardModule();
    final userModule = _$UserModule();
    final inventoryModule = _$InventoryModule();
    gh.lazySingleton<_i558.FlutterSecureStorage>(
      () => externalModule.secureStorage,
    );
    gh.lazySingleton<_i161.InternetConnection>(
      () => externalModule.internetConnection,
    );
    gh.lazySingleton<_i169.NotificationService>(
      () => coreModule.notificationService,
    );
    gh.lazySingleton<_i518.ThemeStorage>(() => coreModule.themeStorage);
    gh.lazySingleton<_i823.BottomNavCubit>(() => coreModule.bottomNavCubit);
    gh.lazySingleton<_i269.ThemeCubit>(() => coreModule.themeCubit);
    gh.lazySingleton<_i522.FlutterSecureStorageServices>(
      () => coreModule.provideSecureStorageServices(
        gh<_i558.FlutterSecureStorage>(),
      ),
    );
    gh.lazySingleton<_i257.DioApiClient>(
      () => coreModule.dioApiClient(gh<_i522.FlutterSecureStorageServices>()),
    );
    gh.singleton<_i310.ConnectionChecker>(
      () => coreModule.provideConnectionChecker(gh<_i161.InternetConnection>()),
    );
    gh.factory<_i1037.AuthenticationLocalDataSource>(
      () => authenticationModule.provideAuthLocalDataSource(
        gh<_i522.FlutterSecureStorageServices>(),
      ),
    );
    gh.lazySingleton<_i512.HttpErrorHandler>(
      () => coreModule.provideHttpErrorHandler(gh<_i310.ConnectionChecker>()),
    );
    gh.factory<_i322.AuthenticationRemoteDataSource>(
      () => authenticationModule.provideAuthRemoteDataSource(
        gh<_i257.DioApiClient>(),
        gh<_i512.HttpErrorHandler>(),
      ),
    );
    gh.factory<_i169.OrderRemoteDataSource>(
      () => orderModule.provideOrderRemoteDataSource(
        gh<_i257.DioApiClient>(),
        gh<_i512.HttpErrorHandler>(),
      ),
    );
    gh.factory<_i148.DashboardRemoteDataSource>(
      () => dashboardModule.provideDashboardRemoteDataSource(
        gh<_i257.DioApiClient>(),
        gh<_i512.HttpErrorHandler>(),
      ),
    );
    gh.factory<_i1005.UserRemoteDatasource>(
      () => userModule.provideUserRemoteDataSource(
        gh<_i257.DioApiClient>(),
        gh<_i512.HttpErrorHandler>(),
      ),
    );
    gh.factory<_i369.InventoryRemoteDataSource>(
      () => inventoryModule.provideInventoryRemoteDataSource(
        gh<_i257.DioApiClient>(),
        gh<_i512.HttpErrorHandler>(),
      ),
    );
    gh.lazySingleton<_i541.DashboardRepository>(
      () => dashboardModule.provideDashboardRepository(
        gh<_i148.DashboardRemoteDataSource>(),
      ),
    );
    gh.lazySingleton<_i550.UserRepository>(
      () => userModule.provideUserRepository(gh<_i1005.UserRemoteDatasource>()),
    );
    gh.lazySingleton<_i574.OrderRepository>(
      () =>
          orderModule.provideOrderRepository(gh<_i169.OrderRemoteDataSource>()),
    );
    gh.lazySingleton<_i998.CreateOrderUseCase>(
      () => orderModule.provideCreateOrderUseCase(gh<_i574.OrderRepository>()),
    );
    gh.lazySingleton<_i387.GetOrdersUseCase>(
      () => orderModule.provideGetOrdersUseCase(gh<_i574.OrderRepository>()),
    );
    gh.lazySingleton<_i34.AssignAgentToOrderUseCase>(
      () => orderModule.provideAssignAgentToOrderUseCase(
        gh<_i574.OrderRepository>(),
      ),
    );
    gh.lazySingleton<_i1069.CancelOrderUseCase>(
      () => orderModule.provideCancelOrderUseCase(gh<_i574.OrderRepository>()),
    );
    gh.lazySingleton<_i795.CompleteOrderUseCase>(
      () =>
          orderModule.provideCompleteOrderUseCase(gh<_i574.OrderRepository>()),
    );
    gh.lazySingleton<_i729.ValidateOrderQrCodeUseCase>(
      () => orderModule.provideValidateOrderQrCodeUseCase(
        gh<_i574.OrderRepository>(),
      ),
    );
    gh.lazySingleton<_i715.UpdateOrderUseCase>(
      () => orderModule.provideUpdateOrderUseCase(gh<_i574.OrderRepository>()),
    );
    gh.lazySingleton<_i386.DeleteOrderUseCase>(
      () => orderModule.provideDeleteOrderUseCase(gh<_i574.OrderRepository>()),
    );
    gh.lazySingleton<_i383.GetAdminDashboardUseCase>(
      () => dashboardModule.provideGetAdminDashboardUseCase(
        gh<_i541.DashboardRepository>(),
      ),
    );
    gh.lazySingleton<_i239.GetCustomerDashboardUseCase>(
      () => dashboardModule.provideGetCustomerDashboardUseCase(
        gh<_i541.DashboardRepository>(),
      ),
    );
    gh.lazySingleton<_i500.GetAgentDashboardUseCase>(
      () => dashboardModule.provideGetAgentDashboardUseCase(
        gh<_i541.DashboardRepository>(),
      ),
    );
    gh.lazySingleton<_i249.GetAdminDashboardReportUseCase>(
      () => dashboardModule.provideGetAdminDashboardReportUseCase(
        gh<_i541.DashboardRepository>(),
      ),
    );
    gh.lazySingleton<DashboardBloc>(
      () => dashboardModule.provideDashboardBloc(
        gh<_i383.GetAdminDashboardUseCase>(),
        gh<_i239.GetCustomerDashboardUseCase>(),
        gh<_i500.GetAgentDashboardUseCase>(),
        gh<_i249.GetAdminDashboardReportUseCase>(),
      ),
    );
    gh.lazySingleton<_i900.OrderBloc>(
      () => orderModule.provideOrderBloc(
        gh<_i998.CreateOrderUseCase>(),
        gh<_i387.GetOrdersUseCase>(),
        gh<_i34.AssignAgentToOrderUseCase>(),
        gh<_i1069.CancelOrderUseCase>(),
        gh<_i795.CompleteOrderUseCase>(),
        gh<_i729.ValidateOrderQrCodeUseCase>(),
        gh<_i715.UpdateOrderUseCase>(),
        gh<_i386.DeleteOrderUseCase>(),
      ),
    );
    gh.lazySingleton<_i429.InventoryRepository>(
      () => inventoryModule.provideInventoryRepository(
        gh<_i369.InventoryRemoteDataSource>(),
      ),
    );
    gh.lazySingleton<_i555.AuthenticationRepository>(
      () => authenticationModule.provideAuthRepository(
        gh<_i322.AuthenticationRemoteDataSource>(),
        gh<_i1037.AuthenticationLocalDataSource>(),
      ),
    );
    gh.lazySingleton<_i973.RegisterAdminOrAgentUseCase>(
      () => userModule.provideRegisterAdminOrAgentUseCase(
        gh<_i550.UserRepository>(),
      ),
    );
    gh.lazySingleton<_i234.UpdateUserUseCase>(
      () => userModule.provideUpdateUserUseCase(gh<_i550.UserRepository>()),
    );
    gh.lazySingleton<_i63.DeleteUserUseCase>(
      () => userModule.provideDeleteUserUseCase(gh<_i550.UserRepository>()),
    );
    gh.lazySingleton<_i25.SubscribeToNotificationsUseCase>(
      () => userModule.provideSubscribeToNotificationsUseCase(
        gh<_i550.UserRepository>(),
      ),
    );
    gh.lazySingleton<_i874.UnsubscribeFromNotificationsUseCase>(
      () => userModule.provideUnsubscribeFromNotificationsUseCase(
        gh<_i550.UserRepository>(),
      ),
    );
    gh.lazySingleton<_i87.GetAllUsersUseCase>(
      () => userModule.provideGetAllUsersUseCase(gh<_i550.UserRepository>()),
    );
    gh.lazySingleton<_i968.GetCurrentUserUseCase>(
      () => userModule.provideGetCurrentUserUseCase(gh<_i550.UserRepository>()),
    );
    gh.lazySingleton<_i100.GetCylindersUseCase>(
      () => inventoryModule.provideGetCylindersUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i810.GetCylinderByIdUseCase>(
      () => inventoryModule.provideGetCylinderByIdUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i237.CreateCylinderUseCase>(
      () => inventoryModule.provideCreateCylinderUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i118.UpdateCylinderUseCase>(
      () => inventoryModule.provideUpdateCylinderUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i750.DeleteCylinderUseCase>(
      () => inventoryModule.provideDeleteCylinderUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i108.RestockCylinderUseCase>(
      () => inventoryModule.provideRestockCylinderUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i990.AdjustCylinderStockUseCase>(
      () => inventoryModule.provideAdjustCylinderStockUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i796.BulkUpdateCylinderStatusUseCase>(
      () => inventoryModule.provideBulkUpdateCylinderStatusUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i435.GetPackagesUseCase>(
      () => inventoryModule.provideGetPackagesUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i967.GetPackageByIdUseCase>(
      () => inventoryModule.provideGetPackageByIdUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i437.CreatePackageUseCase>(
      () => inventoryModule.provideCreatePackageUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i670.UpdatePackageUseCase>(
      () => inventoryModule.provideUpdatePackageUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i184.DeletePackageUseCase>(
      () => inventoryModule.provideDeletePackageUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i853.RestockPackageUseCase>(
      () => inventoryModule.provideRestockPackageUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i799.AdjustPackageStockUseCase>(
      () => inventoryModule.provideAdjustPackageStockUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i3.BulkUpdatePackageStatusUseCase>(
      () => inventoryModule.provideBulkUpdatePackageStatusUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i472.GetSparePartsUseCase>(
      () => inventoryModule.provideGetSparePartsUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i672.GetSparePartByIdUseCase>(
      () => inventoryModule.provideGetSparePartByIdUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i892.CreateSparePartUseCase>(
      () => inventoryModule.provideCreateSparePartUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i954.UpdateSparePartUseCase>(
      () => inventoryModule.provideUpdateSparePartUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i844.DeleteSparePartUseCase>(
      () => inventoryModule.provideDeleteSparePartUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i644.RestockSparePartUseCase>(
      () => inventoryModule.provideRestockSparePartUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i132.AdjustSparePartStockUseCase>(
      () => inventoryModule.provideAdjustSparePartStockUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i597.BulkUpdateSparePartStatusUseCase>(
      () => inventoryModule.provideBulkUpdateSparePartStatusUseCase(
        gh<_i429.InventoryRepository>(),
      ),
    );
    gh.lazySingleton<_i247.LoginUseCase>(
      () => authenticationModule.provideLoginUseCase(
        gh<_i555.AuthenticationRepository>(),
      ),
    );
    gh.lazySingleton<_i67.LogoutUseCase>(
      () => authenticationModule.provideLogoutUseCase(
        gh<_i555.AuthenticationRepository>(),
      ),
    );
    gh.lazySingleton<_i1001.CheckAuthenticationUseCase>(
      () => authenticationModule.provideCheckUserAuthenticationUseCase(
        gh<_i555.AuthenticationRepository>(),
      ),
    );
    gh.lazySingleton<_i573.CompleteOnboardingUseCase>(
      () => authenticationModule.provideCompleteOnboardingUseCase(
        gh<_i555.AuthenticationRepository>(),
      ),
    );
    gh.lazySingleton<_i38.ResendOtpUseCase>(
      () => authenticationModule.provideSendOtpUseCase(
        gh<_i555.AuthenticationRepository>(),
      ),
    );
    gh.lazySingleton<_i200.VerifyOtpUseCase>(
      () => authenticationModule.provideVerifyOtpUseCase(
        gh<_i555.AuthenticationRepository>(),
      ),
    );
    gh.lazySingleton<_i65.InventoryBloc>(
      () => inventoryModule.provideInventoryBloc(
        gh<_i100.GetCylindersUseCase>(),
        gh<_i810.GetCylinderByIdUseCase>(),
        gh<_i237.CreateCylinderUseCase>(),
        gh<_i118.UpdateCylinderUseCase>(),
        gh<_i750.DeleteCylinderUseCase>(),
        gh<_i108.RestockCylinderUseCase>(),
        gh<_i990.AdjustCylinderStockUseCase>(),
        gh<_i796.BulkUpdateCylinderStatusUseCase>(),
        gh<_i435.GetPackagesUseCase>(),
        gh<_i967.GetPackageByIdUseCase>(),
        gh<_i437.CreatePackageUseCase>(),
        gh<_i670.UpdatePackageUseCase>(),
        gh<_i184.DeletePackageUseCase>(),
        gh<_i853.RestockPackageUseCase>(),
        gh<_i799.AdjustPackageStockUseCase>(),
        gh<_i3.BulkUpdatePackageStatusUseCase>(),
        gh<_i472.GetSparePartsUseCase>(),
        gh<_i672.GetSparePartByIdUseCase>(),
        gh<_i892.CreateSparePartUseCase>(),
        gh<_i954.UpdateSparePartUseCase>(),
        gh<_i844.DeleteSparePartUseCase>(),
        gh<_i644.RestockSparePartUseCase>(),
        gh<_i132.AdjustSparePartStockUseCase>(),
        gh<_i597.BulkUpdateSparePartStatusUseCase>(),
      ),
    );
    gh.lazySingleton<UserBloc>(
      () => userModule.provideUserBloc(
        gh<_i973.RegisterAdminOrAgentUseCase>(),
        gh<_i234.UpdateUserUseCase>(),
        gh<_i63.DeleteUserUseCase>(),
        gh<_i25.SubscribeToNotificationsUseCase>(),
        gh<_i874.UnsubscribeFromNotificationsUseCase>(),
        gh<_i87.GetAllUsersUseCase>(),
        gh<_i968.GetCurrentUserUseCase>(),
      ),
    );
    gh.lazySingleton<AuthenticationBloc>(
      () => authenticationModule.provideAuthenticationBloc(
        gh<_i247.LoginUseCase>(),
        gh<_i67.LogoutUseCase>(),
        gh<_i1001.CheckAuthenticationUseCase>(),
        gh<_i573.CompleteOnboardingUseCase>(),
        gh<_i38.ResendOtpUseCase>(),
        gh<_i200.VerifyOtpUseCase>(),
      ),
    );
    return this;
  }
}

class _$ExternalModule extends _i213.ExternalModule {}

class _$CoreModule extends _i320.CoreModule {}

class _$AuthenticationModule extends AuthenticationModule {}

class _$OrderModule extends _i503.OrderModule {}

class _$DashboardModule extends DashboardModule {}

class _$UserModule extends UserModule {}

class _$InventoryModule extends _i40.InventoryModule {}
