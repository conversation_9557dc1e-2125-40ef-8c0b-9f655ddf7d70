import 'package:fpdart/fpdart.dart';

import '../../../../../core/constants/api_end_points.dart';
import '../../../../../core/enums/dashboard_report_type.dart';
import '../../../../../core/enums/http_method.dart';
import '../../../../../core/errors/app_failure.dart';
import '../../../../../core/errors/http_error_handler.dart';
import '../../../../../core/network/api_client/dio_api_client.dart';
import '../../models/admin_dashboard_model.dart';
import '../../models/admin_dashboard_report_model.dart';
import '../../models/agent_dashboard_model.dart';
import '../../models/customer_dashboard_model.dart';

abstract class DashboardRemoteDataSource {
  FutureEitherFailOr<AdminDashboardModel> getAdminDashboard();

  FutureEitherFailOr<CustomerDashboardModel> getCustomerDashboard();

  FutureEitherFailOr<AgentDashboardModel> getAgentDashboard();

  FutureEitherFailOr<AdminDashboardReportModel> getAdminDashboardReport({
    required DashboardReportType reportType,
    required DateTime startDate,
    required DateTime endDate,
  });
}

class DashboardRemoteDataSourceImpl implements DashboardRemoteDataSource {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  const DashboardRemoteDataSourceImpl({
    required this.dioApiClient,
    required this.httpErrorHandler,
  });

  @override
  FutureEitherFailOr<AdminDashboardModel> getAdminDashboard() async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) =>
          AdminDashboardModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.adminDashboard,
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final dashboard = apiResponse.getNonNullableData();
      return right(dashboard);
    });
  }

  @override
  FutureEitherFailOr<CustomerDashboardModel> getCustomerDashboard() async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) =>
          CustomerDashboardModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.customerDashboard,
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final dashboard = apiResponse.getNonNullableData();
      return right(dashboard);
    });
  }

  @override
  FutureEitherFailOr<AgentDashboardModel> getAgentDashboard() async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => AgentDashboardModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.agentDashboard,
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final dashboard = apiResponse.getNonNullableData();
      return right(dashboard);
    });
  }

  @override
  FutureEitherFailOr<AdminDashboardReportModel> getAdminDashboardReport({
    required DashboardReportType reportType,
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) =>
          AdminDashboardReportModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.adminDashboardReport,
        queryParameters: {
          'reportType': reportType.name,
          'startDate': startDate.toIso8601String(),
          'endDate': endDate.toIso8601String(),
        },
      ),
    );
    return response.fold((failure) => left(failure), (apiResponse) {
      final dashboardReport = apiResponse.getNonNullableData();
      return right(dashboardReport);
    });
  }
}
