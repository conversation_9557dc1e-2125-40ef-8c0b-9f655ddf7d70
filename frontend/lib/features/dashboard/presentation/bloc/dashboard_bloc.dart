import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/enums/dashboard_report_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../../../../core/utils/helpers/bloc_helper.dart';
import '../../domain/entities/admin_dashboard_entity.dart';
import '../../domain/entities/agent_dashboard_entity.dart';
import '../../domain/entities/customer_dashboard_entity.dart';
import '../../domain/entities/dashboard_report_entity.dart';
import '../../domain/params/get_admin_dashboard_report_params.dart';
import '../../domain/usecases/get_admin_dashboard_report.dart';
import '../../domain/usecases/get_admin_dashboard_usecase.dart';
import '../../domain/usecases/get_agent_dashboard_usecase.dart';
import '../../domain/usecases/get_customer_dashboard_usecase.dart';

part 'dashboard_event.dart';
part 'dashboard_state.dart';

class DashboardBloc extends Bloc<DashboardEvent, DashboardState> {
  final GetAdminDashboardUseCase getAdminDashboardUseCase;
  final GetCustomerDashboardUseCase getCustomerDashboardUseCase;
  final GetAgentDashboardUseCase getAgentDashboardUseCase;
  final GetAdminDashboardReportUseCase getAdminDashboardReportUseCase;

  DashboardBloc({
    required this.getAdminDashboardUseCase,
    required this.getCustomerDashboardUseCase,
    required this.getAgentDashboardUseCase,
    required this.getAdminDashboardReportUseCase,
  }) : super(DashboardInitial()) {
    on<GetAdminDashboardEvent>(_onGetAdminDashboard);
    on<GetCustomerDashboardEvent>(_onGetCustomerDashboard);
    on<GetAgentDashboardEvent>(_onGetAgentDashboard);
    on<GetAdminDashboardReportEvent>(_onGetAdminDashboardReport);
  }

  AdminDashboardEntity? _adminDashboard;
  AdminDashboardEntity? get adminDashboard => _adminDashboard;

  CustomerDashboardEntity? _customerDashboard;
  CustomerDashboardEntity? get customerDashboard => _customerDashboard;

  AgentDashboardEntity? _agentDashboard;
  AgentDashboardEntity? get agentDashboard => _agentDashboard;

  AdminDashboardReportEntity? _adminDashboardReport;
  AdminDashboardReportEntity? get adminDashboardReport => _adminDashboardReport;

  Future<void> _onGetAdminDashboard(
    GetAdminDashboardEvent event,
    Emitter<DashboardState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<AdminDashboardEntity, DashboardState>(
      emit: emit,
      loadingState: GetAdminDashboardLoading(),
      callUseCase: getAdminDashboardUseCase(params: NoParams()),
      onSuccess: (dashboard) {
        _adminDashboard = dashboard;
        return GetAdminDashboardSuccess(dashboard: dashboard);
      },
      onFailure: (failure) => GetAdminDashboardFailure(appFailure: failure),
    );
  }

  Future<void> _onGetCustomerDashboard(
    GetCustomerDashboardEvent event,
    Emitter<DashboardState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<
      CustomerDashboardEntity,
      DashboardState
    >(
      emit: emit,
      loadingState: GetCustomerDashboardLoading(),
      callUseCase: getCustomerDashboardUseCase(params: NoParams()),
      onSuccess: (dashboard) {
        _customerDashboard = dashboard;
        return GetCustomerDashboardSuccess(dashboard: dashboard);
      },
      onFailure: (failure) => GetCustomerDashboardFailure(appFailure: failure),
    );
  }

  Future<void> _onGetAgentDashboard(
    GetAgentDashboardEvent event,
    Emitter<DashboardState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<AgentDashboardEntity, DashboardState>(
      emit: emit,
      loadingState: GetAgentDashboardLoading(),
      callUseCase: getAgentDashboardUseCase(params: NoParams()),
      onSuccess: (dashboard) {
        _agentDashboard = dashboard;
        return GetAgentDashboardSuccess(dashboard: dashboard);
      },
      onFailure: (failure) => GetAgentDashboardFailure(appFailure: failure),
    );
  }

  Future<void> _onGetAdminDashboardReport(
    GetAdminDashboardReportEvent event,
    Emitter<DashboardState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<
      AdminDashboardReportEntity,
      DashboardState
    >(
      emit: emit,
      loadingState: GetAdminDashboardReportLoading(),
      callUseCase: getAdminDashboardReportUseCase(
        params: GetAdminDashboardParams(
          startDate: event.startDate,
          endDate: event.endDate,
          reportType: event.reportType,
        ),
      ),
      onSuccess: (report) {
        _adminDashboardReport = report;
        return GetAdminDashboardReportSuccess(report: report);
      },
      onFailure: (failure) =>
          GetAdminDashboardReportFailure(appFailure: failure),
    );
  }
}
