part of 'dashboard_bloc.dart';

sealed class DashboardState extends Equatable {
  const DashboardState();

  @override
  List<Object?> get props => [];
}

final class DashboardInitial extends DashboardState {}

///--------- Get Admin Dashboard States ---------
final class GetAdminDashboardLoading extends DashboardState {}

final class GetAdminDashboardSuccess extends DashboardState {
  final AdminDashboardEntity dashboard;

  const GetAdminDashboardSuccess({required this.dashboard});

  @override
  List<Object?> get props => [dashboard];
}

final class GetAdminDashboardFailure extends DashboardState {
  final AppFailure appFailure;

  const GetAdminDashboardFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

///--------- Get Customer Dashboard States ---------
final class GetCustomerDashboardLoading extends DashboardState {}

final class GetCustomerDashboardSuccess extends DashboardState {
  final CustomerDashboardEntity dashboard;

  const GetCustomerDashboardSuccess({required this.dashboard});

  @override
  List<Object?> get props => [dashboard];
}

final class GetCustomerDashboardFailure extends DashboardState {
  final AppFailure appFailure;

  const GetCustomerDashboardFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

///--------- Get Agent Dashboard States ---------
final class GetAgentDashboardLoading extends DashboardState {}

final class GetAgentDashboardSuccess extends DashboardState {
  final AgentDashboardEntity dashboard;

  const GetAgentDashboardSuccess({required this.dashboard});

  @override
  List<Object?> get props => [dashboard];
}

final class GetAgentDashboardFailure extends DashboardState {
  final AppFailure appFailure;

  const GetAgentDashboardFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

///--------- Get Admin Dashboard Report States ---------
final class GetAdminDashboardReportLoading extends DashboardState {}

final class GetAdminDashboardReportSuccess extends DashboardState {
  final AdminDashboardReportEntity report;

  const GetAdminDashboardReportSuccess({required this.report});

  @override
  List<Object?> get props => [report];
}

final class GetAdminDashboardReportFailure extends DashboardState {
  final AppFailure appFailure;

  const GetAdminDashboardReportFailure({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}
