import '../../../../core/enums/dashboard_report_type.dart';
import '../../../../core/errors/app_failure.dart';
import '../entities/admin_dashboard_entity.dart';
import '../entities/agent_dashboard_entity.dart';
import '../entities/customer_dashboard_entity.dart';
import '../entities/dashboard_report_entity.dart';

abstract class DashboardRepository {
  FutureEitherFailOr<AdminDashboardEntity> getAdminDashboard();

  FutureEitherFailOr<CustomerDashboardEntity> getCustomerDashboard();

  FutureEitherFailOr<AgentDashboardEntity> getAgentDashboard();

  FutureEitherFailOr<AdminDashboardReportEntity> getAdminDashboardReport({
    required DashboardReportType reportType,
    required DateTime startDate,
    required DateTime endDate,
  });
}
