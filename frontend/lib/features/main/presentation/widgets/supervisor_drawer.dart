import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/router/extension/navigation_extension.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/utils/extensions/build_context_extensions.dart';
import 'package:frontend/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:frontend/features/authentication/presentation/pages/login_page.dart';
import 'package:frontend/features/main/presentation/pages/supervisor/supervisor_dashboard_page.dart';

class SupervisorDrawer extends StatelessWidget {
  const SupervisorDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthenticationBloc, AuthenticationState>(
      listener: (context, state) {
        if (state is Unauthenticated) {
          context.pushAndRemoveUntilRoute(const LoginPage());
        }
      },
      child: Drawer(
        backgroundColor: context.appColors.surfaceColor,
        child: Safe<PERSON>rea(
          child: Column(
            children: [
              _buildDrawerHeader(context),
              const SizedBox(height: 20),
              Expanded(
                child: Column(
                  children: [
                    // Main content items
                    Expanded(
                      child: ListView(
                        padding: EdgeInsets.zero,
                        children: [
                          _buildDrawerItem(
                            context,
                            icon: Icons.dashboard,
                            title: 'Dashboard',
                            onTap: () {
                              Navigator.pop(context);
                            },
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.assignment,
                            title: 'Order Assignment',
                            onTap: () {
                              Navigator.pop(context);
                              // TODO: Navigate to order assignment page
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'Order Assignment - Coming Soon',
                                  ),
                                ),
                              );
                            },
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.today,
                            title: 'Today\'s Orders',
                            onTap: () {
                              Navigator.pop(context);
                              // TODO: Navigate to today's orders page
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'Today\'s Orders - Coming Soon',
                                  ),
                                ),
                              );
                            },
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.people,
                            title: 'Available Agents',
                            onTap: () {
                              Navigator.pop(context);
                              // TODO: Navigate to agents page
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'Available Agents - Coming Soon',
                                  ),
                                ),
                              );
                            },
                          ),
                          _buildDrawerItem(
                            context,
                            icon: Icons.analytics,
                            title: 'Today\'s Reports',
                            onTap: () {
                              Navigator.pop(context);
                              // TODO: Navigate to reports page
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'Today\'s Reports - Coming Soon',
                                  ),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),

                    // Logout button at the bottom
                    Padding(
                      padding: EdgeInsets.all(16.w),
                      child: Column(
                        children: [
                          const Divider(),
                          _buildDrawerItem(
                            context,
                            icon: Icons.logout,
                            title: 'Logout',
                            textColor: context.appColors.errorColor,
                            onTap: () => _showLogoutDialog(context),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDrawerHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20.w),
      width: context.screenWidth,
      decoration: BoxDecoration(
        color: context.appColors.primaryColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(16.r),
          bottomRight: Radius.circular(16.r),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: 30.r,
            backgroundColor: Colors.white,
            child: Icon(
              Icons.supervisor_account,
              size: 30.sp,
              color: context.appColors.primaryColor,
            ),
          ),
          SizedBox(height: 12.h),
          Text(
            'Supervisor Panel',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            'Today\'s Operations',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: textColor ?? context.appColors.textColor,
        size: 24.sp,
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          color: textColor ?? context.appColors.textColor,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      contentPadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 4.h),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: context.appColors.surfaceColor,
          title: Text(
            'Logout',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: context.appColors.textColor,
            ),
          ),
          content: Text(
            'Are you sure you want to logout?',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.textColor,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: TextStyle(color: context.appColors.textColor),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                context.read<AuthenticationBloc>().add(LogoutEvent());
              },
              child: Text(
                'Logout',
                style: TextStyle(color: context.appColors.errorColor),
              ),
            ),
          ],
        );
      },
    );
  }
}
