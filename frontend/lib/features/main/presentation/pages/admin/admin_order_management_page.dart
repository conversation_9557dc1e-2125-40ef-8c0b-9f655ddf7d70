import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/router/extension/navigation_extension.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/order_status.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/features/order/domain/entities/order_entity.dart';
import 'package:frontend/features/order/presentation/bloc/order_bloc.dart';
import 'package:frontend/features/main/presentation/pages/admin/order_details_page.dart';
import 'package:frontend/features/main/presentation/pages/admin/create_order_page.dart';
import 'package:frontend/features/user-management/presentation/bloc/user_bloc.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';

import '../../../../../core/utils/helpers/snack_bar_helper.dart';
import '../../../../dashboard/domain/entities/customer_dashboard_entity.dart';

class AdminOrderManagementPage extends StatefulWidget {
  const AdminOrderManagementPage({super.key});

  @override
  State<AdminOrderManagementPage> createState() =>
      _AdminOrderManagementPageState();
}

class _AdminOrderManagementPageState extends State<AdminOrderManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  OrderStatus? _selectedStatus;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadOrders();
  }

  void _loadOrders({bool forceRefresh = false}) {
    final orderBloc = context.orderBloc;
    final canRefetch = forceRefresh || orderBloc.orders.isEmpty;
    if (!canRefetch) return;

    context.read<OrderBloc>().add(const GetOrdersEvent());
  }

  void _handleOrderStateChanges(BuildContext context, OrderState state) {
    if (state is AssignAgentToOrderSuccess) {
      SnackBarHelper.showSuccessSnackBar(context, message: state.message);
      // Refresh orders to show updated data
      _loadOrders(forceRefresh: true);
    } else if (state is AssignAgentToOrderFailure) {
      SnackBarHelper.showErrorSnackBar(
        context,
        message: state.appFailure.getErrorMessage(),
      );
    } else if (state is CancelOrderSuccess) {
      SnackBarHelper.showSuccessSnackBar(context, message: state.message);
      // Refresh orders to show updated data
      _loadOrders(forceRefresh: true);
    } else if (state is CancelOrderFailure) {
      SnackBarHelper.showErrorSnackBar(
        context,
        message: state.appFailure.getErrorMessage(),
      );
    } else if (state is CompleteOrderSuccess) {
      SnackBarHelper.showSuccessSnackBar(context, message: state.message);
      // Refresh orders to show updated data
      _loadOrders(forceRefresh: true);
    } else if (state is CompleteOrderFailure) {
      SnackBarHelper.showErrorSnackBar(
        context,
        message: state.appFailure.getErrorMessage(),
      );
    } else if (state is UpdateOrderSuccess) {
      SnackBarHelper.showSuccessSnackBar(context, message: state.message);
      // Refresh orders to show updated data
      _loadOrders(forceRefresh: true);
    } else if (state is UpdateOrderFailure) {
      SnackBarHelper.showErrorSnackBar(
        context,
        message: state.appFailure.getErrorMessage(),
      );
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      body: BlocListener<OrderBloc, OrderState>(
        listener: (context, state) {
          _handleOrderStateChanges(context, state);
        },
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(context),
              _buildStatsOverview(context),
              _buildSearchAndFilters(context),
              _buildTabBar(context),
              Expanded(child: _buildTabBarView(context)),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _createNewOrder(context),
        backgroundColor: context.appColors.primaryColor,
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text(
          'Create Order',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Order Management',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'Monitor and manage all customer orders',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: context.appColors.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              Icons.assignment,
              color: context.appColors.primaryColor,
              size: 24.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsOverview(BuildContext context) {
    return BlocBuilder<OrderBloc, OrderState>(
      buildWhen: (previous, current) {
        return current is GetOrdersLoading ||
            current is GetOrdersSuccess ||
            current is GetOrdersFailure;
      },
      builder: (context, state) {
        if (state is GetOrdersLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        // if (state is GetOrdersFailure) {
        //   return _buildErrorWidget(context, state.appFailure.getErrorMessage());
        // }

        final orders = context.orderBloc.orders;
        final totalOrders = orders.length;
        final pendingOrders = orders
            .where((order) => order.status == OrderStatus.pending)
            .length;
        final inTransitOrders = orders
            .where((order) => order.status == OrderStatus.outForDelivery)
            .length;
        final deliveredOrders = orders
            .where((order) => order.status == OrderStatus.delivered)
            .length;

        return Container(
          margin: EdgeInsets.symmetric(horizontal: 16.w),
          child: Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  context,
                  'Total Orders',
                  // '156',
                  totalOrders.toString(),
                  Icons.receipt_long,
                  Colors.blue,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Pending',
                  // '23',
                  pendingOrders.toString(),
                  Icons.schedule,
                  Colors.orange,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildStatCard(
                  context,
                  'In Transit',
                  // '12',
                  inTransitOrders.toString(),
                  Icons.local_shipping,
                  Colors.indigo,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Delivered',
                  // '89',
                  deliveredOrders.toString(),
                  Icons.check_circle,
                  Colors.green,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(10.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 18.sp),
          SizedBox(height: 6.h),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.textColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 2.h),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                flex: 2,
                child: Container(
                  decoration: BoxDecoration(
                    color: context.appColors.surfaceColor,
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(color: context.appColors.dividerColor),
                  ),
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search orders...',
                      hintStyle: TextStyle(
                        color: context.appColors.subtextColor,
                      ),
                      prefixIcon: Icon(
                        Icons.search,
                        color: context.appColors.subtextColor,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.all(12.w),
                    ),
                    onChanged: (value) {
                      // TODO: Implement search
                    },
                  ),
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: context.appColors.surfaceColor,
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(color: context.appColors.dividerColor),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<OrderStatus?>(
                      value: _selectedStatus,
                      isExpanded: true,
                      hint: Text(
                        'Status',
                        style: TextStyle(
                          color: context.appColors.subtextColor,
                          fontSize: 12.sp,
                        ),
                      ),
                      icon: Icon(
                        Icons.keyboard_arrow_down,
                        color: context.appColors.subtextColor,
                        size: 16.sp,
                      ),
                      style: TextStyle(
                        color: context.appColors.textColor,
                        fontSize: 12.sp,
                      ),
                      items: [
                        const DropdownMenuItem<OrderStatus?>(
                          child: Text('All Status'),
                        ),
                        ...OrderStatus.values.map((status) {
                          return DropdownMenuItem<OrderStatus?>(
                            value: status,
                            child: Text(status.label),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        setState(() {
                          _selectedStatus = value;
                        });
                        _loadOrders();
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              Expanded(
                child: _buildDateFilter(
                  context,
                  'Start Date',
                  _startDate,
                  (date) => setState(() => _startDate = date),
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: _buildDateFilter(
                  context,
                  'End Date',
                  _endDate,
                  (date) => setState(() => _endDate = date),
                ),
              ),
              SizedBox(width: 8.w),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _startDate = null;
                    _endDate = null;
                    _selectedStatus = null;
                    _searchController.clear();
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: context.appColors.primaryColor,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 8.h,
                  ),
                ),
                child: Text('Clear', style: TextStyle(fontSize: 12.sp)),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDateFilter(
    BuildContext context,
    String label,
    DateTime? selectedDate,
    Function(DateTime?) onDateSelected,
  ) {
    return GestureDetector(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: selectedDate ?? DateTime.now(),
          firstDate: DateTime.now().subtract(const Duration(days: 365)),
          lastDate: DateTime.now().add(const Duration(days: 30)),
        );
        onDateSelected(date);
      },
      child: Container(
        padding: EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: context.appColors.surfaceColor,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: context.appColors.dividerColor),
        ),
        child: Row(
          children: [
            Icon(
              Icons.calendar_today,
              color: context.appColors.subtextColor,
              size: 16.sp,
            ),
            SizedBox(width: 4.w),
            Expanded(
              child: Text(
                selectedDate != null
                    ? '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}'
                    : label,
                style: TextStyle(
                  color: selectedDate != null
                      ? context.appColors.textColor
                      : context.appColors.subtextColor,
                  fontSize: 12.sp,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: context.appColors.primaryColor,
          borderRadius: BorderRadius.circular(6.r),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: context.appColors.subtextColor,
        labelStyle: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w600),
        tabs: const [
          Tab(text: 'All Orders'),
          Tab(text: 'Pending'),
          Tab(text: 'Active'),
          Tab(text: 'Completed'),
        ],
      ),
    );
  }

  Widget _buildTabBarView(BuildContext context) {
    return BlocBuilder<OrderBloc, OrderState>(
      builder: (context, state) {
        if (state is GetOrdersLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is GetOrdersFailure) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64.sp,
                  color: context.appColors.subtextColor,
                ),
                SizedBox(height: 16.h),
                Text(
                  'Failed to load orders',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: context.appColors.textColor,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  state.appFailure.message,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16.h),
                ElevatedButton(
                  onPressed: _loadOrders,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        final allOrders = context.orderBloc.orders;
        return TabBarView(
          controller: _tabController,
          children: [
            _buildOrdersList(context, allOrders),
            _buildOrdersList(context, _getPendingOrders(allOrders)),
            _buildOrdersList(context, _getActiveOrders(allOrders)),
            _buildOrdersList(context, _getCompletedOrders(allOrders)),
          ],
        );
      },
    );
  }

  Widget _buildOrdersList(BuildContext context, List<OrderEntity> orders) {
    if (orders.isEmpty) {
      return RefreshIndicator(
        onRefresh: _refreshOrders,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: MediaQuery.of(context).size.height * 0.6,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.receipt_long_outlined,
                    size: 64.sp,
                    color: context.appColors.subtextColor,
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'No orders found',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: context.appColors.textColor,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'Orders will appear here once created',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: context.appColors.subtextColor,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    'Pull down to refresh',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: context.appColors.subtextColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshOrders,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: orders.length,
        itemBuilder: (context, index) {
          final order = orders[index];
          return _buildOrderCard(context, order);
        },
      ),
    );
  }

  Future<void> _refreshOrders() async {
    _loadOrders(forceRefresh: true);
    // Add a small delay to show the refresh indicator
    await Future.delayed(const Duration(milliseconds: 500));
  }

  Widget _buildOrderCard(BuildContext context, OrderEntity order) {
    // Get product summary from items
    final productSummary = _getProductSummary(order.items);
    final totalQuantity = order.items.fold<int>(
      0,
      (sum, item) => sum + item.quantity,
    );

    return InkWell(
      onTap: () {
        _viewOrderDetails(context, order);
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 12.h),
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: context.appColors.surfaceColor,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: context.appColors.dividerColor),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            'Order #${order.id.substring(order.id.length - 6)}',
                            style: Theme.of(context).textTheme.bodyLarge
                                ?.copyWith(
                                  color: context.appColors.textColor,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          SizedBox(width: 8.w),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 8.w,
                              vertical: 2.h,
                            ),
                            decoration: BoxDecoration(
                              color: order.status.color.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(4.r),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  order.status.icon,
                                  color: order.status.color,
                                  size: 12.sp,
                                ),
                                SizedBox(width: 4.w),
                                Text(
                                  order.status.label,
                                  style: TextStyle(
                                    color: order.status.color,
                                    fontSize: 10.sp,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        order.customer.phone,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  '\$${order.totalAmount.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Row(
              children: [
                Expanded(
                  child: _buildOrderInfo(
                    context,
                    'Product',
                    '$productSummary × $totalQuantity',
                    Icons.local_gas_station,
                  ),
                ),
                Expanded(
                  child: _buildOrderInfo(
                    context,
                    'Customer',
                    order.customer.phone,
                    Icons.phone,
                  ),
                ),
              ],
            ),
            if (order.deliveryAgent != null) ...[
              SizedBox(height: 8.h),
              _buildOrderInfo(
                context,
                'Agent',
                order.deliveryAgent!.phone,
                Icons.delivery_dining,
              ),
            ],
            SizedBox(height: 12.h),
            Row(
              children: [
                if (order.status == OrderStatus.pending) ...[
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _assignAgent(context, order),
                      icon: Icon(Icons.person_add, size: 16.sp),
                      label: const Text('Assign Agent'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 8.h),
                      ),
                    ),
                  ),
                  SizedBox(width: 8.w),
                ],
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _viewOrderDetails(context, order),
                    icon: Icon(Icons.visibility, size: 16.sp),
                    label: const Text('View Details'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: context.appColors.primaryColor,
                      side: BorderSide(color: context.appColors.primaryColor),
                      padding: EdgeInsets.symmetric(vertical: 8.h),
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                PopupMenuButton<String>(
                  icon: Icon(
                    Icons.more_vert,
                    color: context.appColors.subtextColor,
                  ),
                  onSelected: (value) =>
                      _handleOrderAction(context, order, value),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit_status',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('Edit Status'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'contact_customer',
                      child: Row(
                        children: [
                          Icon(Icons.phone),
                          SizedBox(width: 8),
                          Text('Contact Customer'),
                        ],
                      ),
                    ),
                    if (order.status != OrderStatus.cancelled)
                      const PopupMenuItem(
                        value: 'cancel',
                        child: Row(
                          children: [
                            Icon(Icons.cancel, color: Colors.red),
                            SizedBox(width: 8),
                            Text(
                              'Cancel Order',
                              style: TextStyle(color: Colors.red),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderInfo(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Row(
      children: [
        Icon(icon, color: context.appColors.subtextColor, size: 14.sp),
        SizedBox(width: 4.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Helper methods
  String _getProductSummary(List<OrderItemEntity> items) {
    if (items.isEmpty) return 'No items';
    if (items.length == 1) {
      return items.first.itemType.replaceAll('_', ' ').toLowerCase();
    }
    return '${items.length} items';
  }

  void _createNewOrder(BuildContext context) {
    context.pushRoute(const CreateOrderPage());
  }

  // Action methods
  void _showBulkActionsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Bulk Actions'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.check_circle, color: Colors.green),
              title: const Text('Mark as Delivered'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Bulk mark as delivered')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.person_add, color: Colors.blue),
              title: const Text('Assign to Agent'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Bulk assign to agent')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.cancel, color: Colors.red),
              title: const Text('Cancel Orders'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Bulk cancel orders')),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _assignAgent(BuildContext context, OrderEntity order) {
    _showAgentSelectionDialog(context, order);
  }

  void _viewOrderDetails(BuildContext context, OrderEntity order) {
    context.pushRoute(OrderDetailsPage(order: order));
  }

  void _handleOrderAction(
    BuildContext context,
    OrderEntity order,
    String action,
  ) {
    switch (action) {
      case 'edit_status':
        _showStatusEditDialog(context, order);
        break;
      case 'contact_customer':
        // TODO: Implement customer contact
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Contact customer ${order.customer.phone}')),
        );
        break;
      case 'cancel':
        _cancelOrder(context, order);
        break;
    }
  }

  void _cancelOrder(BuildContext context, OrderEntity order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Order'),
        content: Text(
          'Are you sure you want to cancel order #${order.id.substring(order.id.length - 6)}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('No'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement actual order cancellation
              context.read<OrderBloc>().add(
                CancelOrderEvent(orderId: order.id),
              );
            },
            child: const Text(
              'Yes, Cancel',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  // Order filtering methods
  List<OrderEntity> _getPendingOrders(List<OrderEntity> orders) {
    return orders
        .where(
          (order) =>
              order.status == OrderStatus.pending ||
              order.status == OrderStatus.confirmed,
        )
        .toList();
  }

  List<OrderEntity> _getActiveOrders(List<OrderEntity> orders) {
    return orders
        .where((order) => order.status == OrderStatus.outForDelivery)
        .toList();
  }

  List<OrderEntity> _getCompletedOrders(List<OrderEntity> orders) {
    return orders
        .where(
          (order) =>
              order.status == OrderStatus.delivered ||
              order.status == OrderStatus.cancelled ||
              order.status == OrderStatus.failed,
        )
        .toList();
  }

  void _showStatusEditDialog(BuildContext context, OrderEntity order) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Order Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order #${order.id.substring(order.id.length - 6)}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Current Status: ${order.status.label}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: order.status.color,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16.h),
            Text(
              'Select new status:',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 12.h),
            ...OrderStatus.values.map((status) {
              final isCurrentStatus = status == order.status;
              final isValidTransition = _isValidStatusTransition(
                order.status,
                status,
              );

              return Container(
                margin: EdgeInsets.only(bottom: 8.h),
                child: InkWell(
                  onTap: isCurrentStatus || !isValidTransition
                      ? null
                      : () => _confirmStatusUpdate(context, order, status),
                  child: Container(
                    padding: EdgeInsets.all(12.w),
                    decoration: BoxDecoration(
                      color: isCurrentStatus
                          ? status.color.withValues(alpha: 0.1)
                          : isValidTransition
                          ? context.appColors.surfaceColor
                          : context.appColors.backgroundColor,
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: isCurrentStatus
                            ? status.color
                            : isValidTransition
                            ? context.appColors.dividerColor
                            : context.appColors.subtextColor.withValues(
                                alpha: 0.3,
                              ),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          status.icon,
                          color: isCurrentStatus || isValidTransition
                              ? status.color
                              : context.appColors.subtextColor,
                          size: 20.sp,
                        ),
                        SizedBox(width: 12.w),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                status.label,
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(
                                      color:
                                          isCurrentStatus || isValidTransition
                                          ? context.appColors.textColor
                                          : context.appColors.subtextColor,
                                      fontWeight: isCurrentStatus
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                    ),
                              ),
                              if (isCurrentStatus)
                                Text(
                                  'Current',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(
                                        color: status.color,
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                              if (!isValidTransition && !isCurrentStatus)
                                Text(
                                  'Not allowed',
                                  style: Theme.of(context).textTheme.bodySmall
                                      ?.copyWith(
                                        color: context.appColors.subtextColor,
                                      ),
                                ),
                            ],
                          ),
                        ),
                        if (isValidTransition && !isCurrentStatus)
                          Icon(
                            Icons.arrow_forward_ios,
                            color: context.appColors.subtextColor,
                            size: 16.sp,
                          ),
                      ],
                    ),
                  ),
                ),
              );
            }),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  bool _isValidStatusTransition(
    OrderStatus currentStatus,
    OrderStatus newStatus,
  ) {
    // Define valid status transitions
    switch (currentStatus) {
      case OrderStatus.pending:
        return [
          OrderStatus.confirmed,
          OrderStatus.cancelled,
          OrderStatus.failed,
        ].contains(newStatus);

      case OrderStatus.confirmed:
        return [
          OrderStatus.outForDelivery,
          OrderStatus.cancelled,
        ].contains(newStatus);

      case OrderStatus.outForDelivery:
        return [
          OrderStatus.delivered,
          OrderStatus.cancelled,
        ].contains(newStatus);

      case OrderStatus.delivered:
        return false; // No transitions from delivered

      case OrderStatus.cancelled:
        return false; // No transitions from cancelled

      case OrderStatus.failed:
        return [
          OrderStatus.pending, // Allow retry
          OrderStatus.cancelled,
        ].contains(newStatus);
    }
  }

  void _confirmStatusUpdate(
    BuildContext context,
    OrderEntity order,
    OrderStatus newStatus,
  ) {
    Navigator.pop(context); // Close status dialog

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Status Update'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Order #${order.id.substring(order.id.length - 6)}'),
            SizedBox(height: 8.h),
            Row(
              children: [
                const Text('From: '),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                  decoration: BoxDecoration(
                    color: order.status.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    order.status.label,
                    style: TextStyle(
                      color: order.status.color,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 4.h),
            Row(
              children: [
                const Text('To: '),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                  decoration: BoxDecoration(
                    color: newStatus.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    newStatus.label,
                    style: TextStyle(
                      color: newStatus.color,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            Text(
              _getStatusUpdateMessage(newStatus),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _updateOrderStatus(context, order, newStatus);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: newStatus.color,
              foregroundColor: Colors.white,
            ),
            child: const Text('Update Status'),
          ),
        ],
      ),
    );
  }

  String _getStatusUpdateMessage(OrderStatus status) {
    switch (status) {
      case OrderStatus.confirmed:
        return 'This will confirm the order and make it ready for assignment.';
      case OrderStatus.outForDelivery:
        return 'This will mark the order as out for delivery. Make sure an agent is assigned.';
      case OrderStatus.delivered:
        return 'This will mark the order as completed and delivered.';
      case OrderStatus.cancelled:
        return 'This will cancel the order and release any reserved inventory.';
      case OrderStatus.failed:
        return 'This will mark the order as failed. Inventory will be released.';
      case OrderStatus.pending:
        return 'This will reset the order to pending status.';
    }
  }

  void _updateOrderStatus(
    BuildContext context,
    OrderEntity order,
    OrderStatus newStatus,
  ) {
    // Use appropriate bloc events based on the status
    switch (newStatus) {
      case OrderStatus.cancelled:
        context.read<OrderBloc>().add(CancelOrderEvent(orderId: order.id));
        break;
      case OrderStatus.delivered:
        context.read<OrderBloc>().add(CompleteOrderEvent(orderId: order.id));
        break;
      default:
        // For other status updates, we would need a general UpdateOrderStatusEvent
        // For now, show a message that this functionality is coming soon
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Status update to ${newStatus.label} is not yet implemented',
            ),
            backgroundColor: Colors.orange,
          ),
        );
        break;
    }
  }

  void _showAgentSelectionDialog(BuildContext context, OrderEntity order) {
    // Load agents when dialog opens
    context.read<UserBloc>().add(
      const GetAllUsersEvent(role: UserRole.agent, isActive: true),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Assign Agent'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400.h,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Order #${order.id.substring(order.id.length - 6)}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                'Customer: ${order.customer.phone}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
              SizedBox(height: 16.h),
              Text(
                'Select an agent:',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
              ),
              SizedBox(height: 12.h),
              Expanded(
                child: BlocBuilder<UserBloc, UserState>(
                  builder: (context, state) {
                    if (state is GetAllUsersLoading) {
                      return const Center(child: CircularProgressIndicator());
                    }

                    if (state is GetAllUsersFailure) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 48.sp,
                              color: Colors.red,
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              'Failed to load agents',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                            SizedBox(height: 8.h),
                            ElevatedButton(
                              onPressed: () => context.read<UserBloc>().add(
                                const GetAllUsersEvent(
                                  role: UserRole.agent,
                                  isActive: true,
                                ),
                              ),
                              child: const Text('Retry'),
                            ),
                          ],
                        ),
                      );
                    }

                    if (state is GetAllUsersSuccess) {
                      final agents = state.users
                          .where(
                            (user) =>
                                user.role == UserRole.agent && user.isActive,
                          )
                          .toList();

                      if (agents.isEmpty) {
                        return Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.person_off,
                                size: 48.sp,
                                color: context.appColors.subtextColor,
                              ),
                              SizedBox(height: 8.h),
                              Text(
                                'No agents available',
                                style: Theme.of(context).textTheme.bodyMedium
                                    ?.copyWith(
                                      color: context.appColors.subtextColor,
                                    ),
                              ),
                            ],
                          ),
                        );
                      }

                      return ListView.builder(
                        itemCount: agents.length,
                        itemBuilder: (context, index) {
                          final agent = agents[index];
                          return _buildAgentCard(context, agent, order);
                        },
                      );
                    }

                    return const SizedBox.shrink();
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildAgentCard(
    BuildContext context,
    UserEntity agent,
    OrderEntity order,
  ) {
    final isOnDuty = agent.agentMetadata?.isOnDuty ?? false;
    final vehicleInfo = agent.agentMetadata?.vehicle;
    final rating = agent.agentMetadata?.rating;

    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      child: InkWell(
        onTap: () => _confirmAgentAssignment(context, agent, order),
        child: Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: isOnDuty
                  ? Colors.green.withValues(alpha: 0.3)
                  : context.appColors.dividerColor,
            ),
          ),
          child: Row(
            children: [
              CircleAvatar(
                radius: 20.r,
                backgroundColor: isOnDuty ? Colors.green : Colors.grey,
                child: Icon(
                  Icons.delivery_dining,
                  color: Colors.white,
                  size: 20.sp,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          agent.phone,
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(fontWeight: FontWeight.w600),
                        ),
                        SizedBox(width: 8.w),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 6.w,
                            vertical: 2.h,
                          ),
                          decoration: BoxDecoration(
                            color: isOnDuty
                                ? Colors.green.withValues(alpha: 0.1)
                                : Colors.orange.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                          child: Text(
                            isOnDuty ? 'On Duty' : 'Off Duty',
                            style: TextStyle(
                              color: isOnDuty ? Colors.green : Colors.orange,
                              fontSize: 10.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (agent.email != null) ...[
                      SizedBox(height: 2.h),
                      Text(
                        agent.email!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                      ),
                    ],
                    if (vehicleInfo != null) ...[
                      SizedBox(height: 2.h),
                      Text(
                        '${vehicleInfo.type.name.toUpperCase()}${vehicleInfo.number != null ? ' - ${vehicleInfo.number}' : ''}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                      ),
                    ],
                    if (rating != null) ...[
                      SizedBox(height: 4.h),
                      Row(
                        children: [
                          Icon(Icons.star, color: Colors.amber, size: 12.sp),
                          SizedBox(width: 2.w),
                          Text(
                            rating.toStringAsFixed(1),
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(
                                  color: context.appColors.subtextColor,
                                ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: context.appColors.subtextColor,
                size: 16.sp,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _confirmAgentAssignment(
    BuildContext context,
    UserEntity agent,
    OrderEntity order,
  ) {
    Navigator.pop(context); // Close agent selection dialog

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Agent Assignment'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Order #${order.id.substring(order.id.length - 6)}'),
            SizedBox(height: 8.h),
            Text('Customer: ${order.customer.phone}'),
            SizedBox(height: 8.h),
            Text('Agent: ${agent.phone}'),
            if (agent.email != null) ...[
              SizedBox(height: 4.h),
              Text('Email: ${agent.email}'),
            ],
            SizedBox(height: 12.h),
            Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Text(
                'This will assign the agent to the order and change the status to "Out for Delivery".',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.blue),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _assignAgentToOrder(context, order, agent);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Assign Agent'),
          ),
        ],
      ),
    );
  }

  void _assignAgentToOrder(
    BuildContext context,
    OrderEntity order,
    UserEntity agent,
  ) {
    context.read<OrderBloc>().add(
      AssignAgentToOrderEvent(orderId: order.id, agentId: agent.id),
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Assigning agent ${agent.phone} to order...'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
