import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';

class AdminUsersPage extends StatefulWidget {
  const AdminUsersPage({super.key});

  @override
  State<AdminUsersPage> createState() => _AdminUsersPageState();
}

class _AdminUsersPageState extends State<AdminUsersPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  UserRole? _selectedRole;
  bool? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(context),
            _buildSearchAndFilters(context),
            _buildTabBar(context),
            Expanded(
              child: _buildTabBarView(context),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          _showAddUserDialog(context);
        },
        backgroundColor: context.appColors.primaryColor,
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text(
          'Add User',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'User Management',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  'Manage customers, agents, and admins',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: context.appColors.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              Icons.people,
              color: context.appColors.primaryColor,
              size: 24.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Column(
        children: [
          // Search Bar
          Container(
            decoration: BoxDecoration(
              color: context.appColors.surfaceColor,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: context.appColors.dividerColor,
                width: 1,
              ),
            ),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search users by phone, email...',
                hintStyle: TextStyle(color: context.appColors.subtextColor),
                prefixIcon: Icon(
                  Icons.search,
                  color: context.appColors.subtextColor,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(16.w),
              ),
              onChanged: (value) {
                // TODO: Implement search
              },
            ),
          ),
          SizedBox(height: 12.h),
          // Filters
          Row(
            children: [
              Expanded(
                child: _buildFilterDropdown(
                  'Role',
                  _selectedRole?.value ?? 'All Roles',
                  [
                    'All Roles',
                    'Customer',
                    'Agent',
                    'Admin',
                  ],
                  (value) {
                    setState(() {
                      if (value == 'All Roles') {
                        _selectedRole = null;
                      } else {
                        _selectedRole = UserRole.fromString(value!.toLowerCase());
                      }
                    });
                  },
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildFilterDropdown(
                  'Status',
                  _selectedStatus == null
                      ? 'All Status'
                      : _selectedStatus!
                          ? 'Active'
                          : 'Inactive',
                  [
                    'All Status',
                    'Active',
                    'Inactive',
                  ],
                  (value) {
                    setState(() {
                      if (value == 'All Status') {
                        _selectedStatus = null;
                      } else {
                        _selectedStatus = value == 'Active';
                      }
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown(
    String label,
    String value,
    List<String> items,
    Function(String?) onChanged,
  ) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: context.appColors.dividerColor,
          width: 1,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          isExpanded: true,
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: context.appColors.subtextColor,
          ),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.textColor,
              ),
          items: items.map((String item) {
            return DropdownMenuItem<String>(
              value: item,
              child: Text(item),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: context.appColors.primaryColor,
          borderRadius: BorderRadius.circular(8.r),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: context.appColors.subtextColor,
        labelStyle: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w600,
        ),
        tabs: const [
          Tab(text: 'All'),
          Tab(text: 'Customers'),
          Tab(text: 'Agents'),
          Tab(text: 'Admins'),
        ],
      ),
    );
  }

  Widget _buildTabBarView(BuildContext context) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildUsersList(context, null),
        _buildUsersList(context, UserRole.customer),
        _buildUsersList(context, UserRole.agent),
        _buildUsersList(context, UserRole.admin),
      ],
    );
  }

  Widget _buildUsersList(BuildContext context, UserRole? role) {
    // Mock data - replace with actual data from your repository
    final users = _getMockUsers(role);

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        return _buildUserCard(context, user);
      },
    );
  }

  Widget _buildUserCard(BuildContext context, UserEntity user) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: context.appColors.dividerColor,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 24.r,
                backgroundColor: _getRoleColor(user.role).withValues(alpha: 0.1),
                child: Icon(
                  _getRoleIcon(user.role),
                  color: _getRoleColor(user.role),
                  size: 20.sp,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          user.phone,
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: context.appColors.textColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(width: 8.w),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 8.w,
                            vertical: 2.h,
                          ),
                          decoration: BoxDecoration(
                            color: _getRoleColor(user.role).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(4.r),
                          ),
                          child: Text(
                            user.role.value.toUpperCase(),
                            style: TextStyle(
                              color: _getRoleColor(user.role),
                              fontSize: 10.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    if (user.email != null)
                      Text(
                        user.email!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: context.appColors.subtextColor,
                        ),
                      ),
                    SizedBox(height: 4.h),
                    Row(
                      children: [
                        Icon(
                          user.isActive ? Icons.check_circle : Icons.cancel,
                          color: user.isActive ? Colors.green : Colors.red,
                          size: 16.sp,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          user.isActive ? 'Active' : 'Inactive',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: user.isActive ? Colors.green : Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        if (user.role == UserRole.agent && user.agentMetadata != null)
                          ...[
                            SizedBox(width: 12.w),
                            Icon(
                              user.agentMetadata!.isOnDuty
                                  ? Icons.work
                                  : Icons.work_off,
                              color: user.agentMetadata!.isOnDuty
                                  ? Colors.green
                                  : Colors.orange,
                              size: 16.sp,
                            ),
                            SizedBox(width: 4.w),
                            Text(
                              user.agentMetadata!.isOnDuty ? 'On Duty' : 'Off Duty',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: user.agentMetadata!.isOnDuty
                                    ? Colors.green
                                    : Colors.orange,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                      ],
                    ),
                  ],
                ),
              ),
              PopupMenuButton<String>(
                icon: Icon(
                  Icons.more_vert,
                  color: context.appColors.subtextColor,
                ),
                onSelected: (value) {
                  _handleUserAction(context, user, value);
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'view',
                    child: Row(
                      children: [
                        Icon(Icons.visibility),
                        SizedBox(width: 8),
                        Text('View Details'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit),
                        SizedBox(width: 8),
                        Text('Edit'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: user.isActive ? 'deactivate' : 'activate',
                    child: Row(
                      children: [
                        Icon(user.isActive ? Icons.block : Icons.check_circle),
                        const SizedBox(width: 8),
                        Text(user.isActive ? 'Deactivate' : 'Activate'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Delete', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return Colors.blue;
      case UserRole.agent:
        return Colors.green;
      case UserRole.admin:
        return Colors.purple;
    }
  }

  IconData _getRoleIcon(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return Icons.person;
      case UserRole.agent:
        return Icons.delivery_dining;
      case UserRole.admin:
        return Icons.admin_panel_settings;
    }
  }

  void _handleUserAction(BuildContext context, UserEntity user, String action) {
    switch (action) {
      case 'view':
        _showUserDetails(context, user);
        break;
      case 'edit':
        _showEditUserDialog(context, user);
        break;
      case 'activate':
      case 'deactivate':
        _toggleUserStatus(context, user);
        break;
      case 'delete':
        _showDeleteConfirmation(context, user);
        break;
    }
  }

  void _showUserDetails(BuildContext context, UserEntity user) {
    // TODO: Implement user details dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('View details for ${user.phone}')),
    );
  }

  void _showEditUserDialog(BuildContext context, UserEntity user) {
    // TODO: Implement edit user dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Edit user ${user.phone}')),
    );
  }

  void _showAddUserDialog(BuildContext context) {
    // TODO: Implement add user dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add new user dialog')),
    );
  }

  void _toggleUserStatus(BuildContext context, UserEntity user) {
    // TODO: Implement user status toggle
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '${user.isActive ? 'Deactivated' : 'Activated'} user ${user.phone}',
        ),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, UserEntity user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete User'),
        content: Text('Are you sure you want to delete ${user.phone}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement user deletion
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Deleted user ${user.phone}')),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  List<UserEntity> _getMockUsers(UserRole? role) {
    // Mock data - replace with actual data from your repository
    final allUsers = [
      UserEntity(
        id: '1',
        phone: '+252612345678',
        email: '<EMAIL>',
        role: UserRole.customer,
        isActive: true,
      ),
      UserEntity(
        id: '2',
        phone: '+252612345679',
        email: '<EMAIL>',
        role: UserRole.agent,
        isActive: true,
        agentMetadata: const AgentMetadataEntity(
          isOnDuty: true,
          rating: 4.8,
        ),
      ),
      UserEntity(
        id: '3',
        phone: '+252612345680',
        email: '<EMAIL>',
        role: UserRole.admin,
        isActive: true,
      ),
    ];

    if (role == null) return allUsers;
    return allUsers.where((user) => user.role == role).toList();
  }
}
