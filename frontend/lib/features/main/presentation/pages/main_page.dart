import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/router/extension/navigation_extension.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';

import '../../../authentication/domain/entities/user_entity.dart';
import '../../../authentication/presentation/bloc/authentication_bloc.dart';
import '../../../authentication/presentation/pages/login_page.dart';
import '../../../shared/presentation/bloc/bottom-nav-cubit/bottom_nav_cubit.dart';
import '../../../user-management/presentation/bloc/user_bloc.dart';
import '../../domain/models/tab_config.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  UserRole? _currentUserRole;

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
  }

  Future<void> _loadCurrentUser() async {
    // First try to get user from UserBloc
    final userBloc = context.userBloc;
    final authBloc = context.authenticationBloc;

    userBloc.add(const GetCurrentUserEvent());

    await userBloc.stream.firstWhere((state) {
      return state is GetCurrentUserSuccess || state is GetCurrentUserFailure;
    });

    if (userBloc.state is GetCurrentUserSuccess) {
      final state = userBloc.state as GetCurrentUserSuccess;
      if (mounted) {
        setState(() {
          _currentUserRole = state.user.role;
        });
      }
      return;
    }

    // Fallback to AuthenticationBloc if UserBloc fails
    if (authBloc.state is Authenticated) {
      final state = authBloc.state as Authenticated;
      if (mounted) {
        setState(() {
          _currentUserRole = state.user?.role ?? UserRole.customer;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthenticationBloc, AuthenticationState>(
      builder: (context, authState) {
        // Get current user from authentication state as fallback
        UserEntity? currentUser;
        if (authState is Authenticated && authState.user != null) {
          currentUser = authState.user!;
          _currentUserRole ??= currentUser.role;
        }

        // If no user role determined, show loading
        if (_currentUserRole == null) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        return BlocBuilder<BottomNavCubit, BottomNavState>(
          builder: (context, navState) {
            final selectedIndex = navState.selectedIndex;
            final tabs = RoleBasedTabConfig.getTabsForRole(_currentUserRole!);

            return Scaffold(
              body: _buildBody(tabs, selectedIndex),
              bottomNavigationBar: _buildBottomNavigationBar(
                context,
                tabs,
                selectedIndex,
              ),

              // logout
              // floatingActionButton:
              //     BlocListener<AuthenticationBloc, AuthenticationState>(
              //       listener: (context, state) {
              //         if (state is Unauthenticated) {
              //           context.pushAndRemoveUntilRoute(const LoginPage());
              //         }
              //       },
              //       child: FloatingActionButton(
              //         onPressed: () {
              //           context.authenticationBloc.add(LogoutEvent());
              //         },
              //         child: const Icon(Icons.logout),
              //       ),
              //     ),
            );
          },
        );
      },
    );
  }

  Widget _buildBody(List<TabConfig> tabs, int selectedIndex) {
    if (selectedIndex >= 0 && selectedIndex < tabs.length) {
      return tabs[selectedIndex].page;
    }
    return tabs.isNotEmpty ? tabs[0].page : const SizedBox.shrink();
  }

  Widget _buildBottomNavigationBar(
    BuildContext context,
    List<TabConfig> tabs,
    int selectedIndex,
  ) {
    final navCubit = context.read<BottomNavCubit>();
    final appColors = context.appColors;

    return Container(
      decoration: BoxDecoration(
        color: appColors.surfaceColor,
        boxShadow: [
          BoxShadow(
            color: appColors.textColor.withValues(alpha: 0.1),
            blurRadius: 10.r,
            offset: Offset(0, -2.h),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: selectedIndex,
        onTap: navCubit.changeTab,
        type: BottomNavigationBarType.fixed,
        backgroundColor: appColors.surfaceColor,
        selectedItemColor: appColors.primaryColor,
        unselectedItemColor: appColors.subtextColor,
        selectedLabelStyle: TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 11.sp,
          fontWeight: FontWeight.w500,
        ),
        elevation: 0,
        items: tabs
            .map(
              (tab) => _buildBottomNavBarItem(
                context: context,
                tab: tab,
                isSelected: tabs.indexOf(tab) == selectedIndex,
              ),
            )
            .toList(),
      ),
    );
  }

  BottomNavigationBarItem _buildBottomNavBarItem({
    required BuildContext context,
    required TabConfig tab,
    required bool isSelected,
  }) {
    final appColors = context.appColors;

    return BottomNavigationBarItem(
      icon: Container(
        padding: EdgeInsets.symmetric(vertical: 4.h),
        child: Icon(
          isSelected && tab.activeIcon != null ? tab.activeIcon! : tab.icon,
          size: 24.sp,
        ),
      ),
      label: tab.label,
      backgroundColor: appColors.surfaceColor,
    );
  }
}
