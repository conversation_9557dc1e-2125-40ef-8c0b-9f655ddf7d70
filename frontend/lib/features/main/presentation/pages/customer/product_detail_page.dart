import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/cylinder_status.dart';
import 'package:frontend/core/enums/cylinder_type.dart';
import 'package:frontend/core/enums/package_status.dart';
import 'package:frontend/core/enums/spare_part_status.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/core/utils/extensions/build_context_extensions.dart';
import 'package:frontend/features/inventory/presentation/bloc/inventory_bloc.dart';
import 'package:frontend/features/order/presentation/bloc/order_bloc.dart';
import 'package:frontend/features/dashboard/domain/entities/customer_dashboard_entity.dart';
import 'package:frontend/features/inventory/domain/entities/cylinder_entity.dart';
import 'package:frontend/features/inventory/domain/entities/package_entity.dart';
import 'package:frontend/features/inventory/domain/entities/spare_part_entity.dart';
import 'package:frontend/features/shared/presentation/widgets/custom_textfield.dart';

import '../../../../../core/utils/helpers/snack_bar_helper.dart';

class ProductDetailPage extends StatefulWidget {
  final String productId;
  final String productType;

  const ProductDetailPage({
    super.key,
    required this.productId,
    required this.productType,
  });

  @override
  State<ProductDetailPage> createState() => _ProductDetailPageState();
}

class _ProductDetailPageState extends State<ProductDetailPage> {
  int _quantity = 1;
  final TextEditingController _addressController = TextEditingController();
  final String _selectedPaymentMethod = 'waafi_preauth';

  // Product data
  CylinderEntity? _cylinder;
  PackageEntity? _package;
  SparePartEntity? _sparePart;

  double get _unitPrice {
    switch (widget.productType.toLowerCase()) {
      case 'cylinder':
        return _cylinder?.price ?? 0.0;
      case 'package':
        return _package?.totalPrice ?? 0.0;
      case 'spare_part':
        return _sparePart?.price ?? 0.0;
      default:
        return 0.0;
    }
  }

  double get _totalPrice => _unitPrice * _quantity;

  String get _productName {
    switch (widget.productType.toLowerCase()) {
      case 'cylinder':
        return _cylinder != null
            ? '${_cylinder!.type.name} - ${_cylinder!.material.name}'
            : 'Gas Cylinder';
      case 'package':
        return _package?.name ?? 'Full Package';
      case 'spare_part':
        return _sparePart?.name ?? 'Spare Part';
      default:
        return 'Product';
    }
  }

  String get _productDescription {
    switch (widget.productType.toLowerCase()) {
      case 'cylinder':
        return _cylinder?.description ?? '';
      case 'package':
        return _package?.description ?? '';
      case 'spare_part':
        return _sparePart?.description ?? '';
      default:
        return '';
    }
  }

  String get _productImageUrl {
    switch (widget.productType.toLowerCase()) {
      case 'cylinder':
        return _cylinder?.formattedImageUrl ?? '';
      case 'package':
        return _package?.imageUrl ?? '';
      case 'spare_part':
        return ''; // Spare parts don't have imageUrl in the entity
      default:
        return '';
    }
  }

  bool get _isAvailable {
    switch (widget.productType.toLowerCase()) {
      case 'cylinder':
        return _cylinder?.status == CylinderStatus.active &&
            (_cylinder?.availableQuantity ?? 0) > 0;
      case 'package':
        return _package?.status == PackageStatus.active &&
            (_package?.availableQuantity ?? 0) > 0;
      case 'spare_part':
        return _sparePart?.status == SparePartStatus.available &&
            (_sparePart?.availableQuantity ?? 0) > 0;
      default:
        return false;
    }
  }

  @override
  void initState() {
    super.initState();
    _loadProductDetails();
  }

  @override
  void dispose() {
    _addressController.dispose();
    super.dispose();
  }

  void _loadProductDetails() {
    switch (widget.productType.toLowerCase()) {
      case 'cylinder':
        context.read<InventoryBloc>().add(
          GetCylinderByIdEvent(cylinderId: widget.productId),
        );
        break;
      case 'package':
        context.read<InventoryBloc>().add(
          GetPackageByIdEvent(packageId: widget.productId),
        );
        break;
      case 'spare_part':
        context.read<InventoryBloc>().add(
          GetSparePartByIdEvent(sparePartId: widget.productId),
        );
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.backgroundColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: Icon(Icons.arrow_back, color: context.appColors.textColor),
        ),
        title: Text(
          'Product Details',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: BlocListener<InventoryBloc, InventoryState>(
        listener: (context, state) {
          if (state is GetCylinderByIdSuccess) {
            setState(() {
              _cylinder = state.cylinder;
            });
          } else if (state is GetPackageByIdSuccess) {
            setState(() {
              _package = state.package;
            });
          } else if (state is GetSparePartByIdSuccess) {
            setState(() {
              _sparePart = state.sparePart;
            });
          }
        },
        child: _buildBody(context),
      ),
      bottomSheet: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
        child: _buildOrderButton(context),
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    return BlocBuilder<InventoryBloc, InventoryState>(
      builder: (context, state) {
        if (state is GetCylinderByIdLoading ||
            state is GetPackageByIdLoading ||
            state is GetSparePartByIdLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is GetCylinderByIdFailure ||
            state is GetPackageByIdFailure ||
            state is GetSparePartByIdFailure) {
          return _buildErrorWidget(context);
        }

        return SingleChildScrollView(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProductImage(context),
              SizedBox(height: 24.h),
              _buildProductInfo(context),
              SizedBox(height: 24.h),
              _buildQuantitySelector(context),
              SizedBox(height: 24.h),
              // _buildPriceSection(context),
              // SizedBox(height: 24.h),
              _buildDeliveryAddress(context),
              SizedBox(height: 24.h),
              // _buildPaymentMethod(context),
              // SizedBox(height: 32.h),
              SizedBox(height: 200.h),
              // _buildOrderButton(context),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProductImage(BuildContext context) {
    // print('image is : $_productImageUrl');
    return Container(
      height: 200.h,
      width: double.infinity,
      decoration: BoxDecoration(
        color: context.appColors.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: _productImageUrl.trim().isNotEmpty
          ? ClipRRect(
              borderRadius: BorderRadius.circular(16.r),
              child: Image.network(
                _productImageUrl,
                // fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Icon(
                  Icons.local_gas_station,
                  size: 80.sp,
                  color: context.appColors.primaryColor,
                ),
              ),
            )
          : Icon(
              Icons.local_gas_station,
              size: 80.sp,
              color: context.appColors.primaryColor,
            ),
    );
  }

  Widget _buildProductInfo(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.surfaceColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: context.appColors.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  _productName,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: _isAvailable
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Text(
                  _isAvailable ? 'Available' : 'Out of Stock',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: _isAvailable ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          if (_productDescription.isNotEmpty) ...[
            SizedBox(height: 12.h),
            Text(
              _productDescription,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.subtextColor,
              ),
            ),
          ],
          SizedBox(height: 12.h),
          Text(
            'Unit Price: \$${_unitPrice.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: context.appColors.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuantitySelector(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quantity',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),
        Container(
          padding: EdgeInsets.all(4.w),
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: context.appColors.dividerColor),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: _quantity > 1
                    ? () => setState(() => _quantity--)
                    : null,
                icon: Icon(
                  Icons.remove,
                  color: _quantity > 1
                      ? context.appColors.primaryColor
                      : context.appColors.subtextColor,
                ),
              ),
              Container(
                width: 60.w,
                alignment: Alignment.center,
                child: Text(
                  _quantity.toString(),
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              IconButton(
                onPressed: _quantity < 10
                    ? () => setState(() => _quantity++)
                    : null,
                icon: Icon(
                  Icons.add,
                  color: _quantity < 10
                      ? context.appColors.primaryColor
                      : context.appColors.subtextColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPriceSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.primaryColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: context.appColors.primaryColor.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Unit Price:',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: context.appColors.textColor,
                ),
              ),
              Text(
                '\$${_unitPrice.toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Quantity:',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: context.appColors.textColor,
                ),
              ),
              Text(
                '$_quantity',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          Divider(
            height: 24.h,
            color: context.appColors.primaryColor.withValues(alpha: 0.3),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Price:',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: context.appColors.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '\$${_totalPrice.toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: context.appColors.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryAddress(BuildContext context) {
    final textTheme = context.textTheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Delivery Address',
          style: textTheme.bodyLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),
        CustomTextField(
          controller: _addressController,
          // maxLine: 2,
          contentPadding: EdgeInsets.all(25.w),
          hintText: 'Enter your delivery address',
          hintStyle: textTheme.bodyMedium?.copyWith(
            color: context.appColors.subtextColor,
          ),
          prefixIcon: Icon(
            Icons.location_on,
            color: context.appColors.primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentMethod(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Payment Method',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),
        Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: context.appColors.surfaceColor,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(color: context.appColors.dividerColor),
          ),
          child: Row(
            children: [
              Icon(Icons.payment, color: context.appColors.primaryColor),
              SizedBox(width: 12.w),
              Text(
                'Waafi Pay',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: context.appColors.textColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              Icon(Icons.check_circle, color: Colors.green, size: 20.sp),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOrderButton(BuildContext context) {
    return BlocConsumer<OrderBloc, OrderState>(
      listener: (context, state) {
        if (state is CreateOrderSuccess) {
          Navigator.pop(context);
          SnackBarHelper.showSuccessSnackBar(
            context,
            message: 'Order placed successfully!',
          );
          context.orderBloc.add(const GetOrdersEvent());
        } else if (state is CreateOrderFailure) {
          SnackBarHelper.showErrorSnackBar(
            context,
            message: state.appFailure.getErrorMessage(),
          );
        }
      },
      builder: (context, state) {
        final isLoading = state is CreateOrderLoading;

        return SizedBox(
          width: double.infinity,
          height: 50.h,
          child: ElevatedButton(
            onPressed: isLoading || !_isAvailable
                ? null
                : () => _placeOrder(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: context.appColors.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
            child: isLoading
                ? SizedBox(
                    width: 20.w,
                    height: 20.h,
                    child: const CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    _isAvailable
                        ? 'Place Order - \$${_totalPrice.toStringAsFixed(2)}'
                        : 'Out of Stock',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        );
      },
    );
  }

  Widget _buildErrorWidget(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: Colors.red),
          SizedBox(height: 16.h),
          Text(
            'Failed to load product details',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: Colors.red),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _loadProductDetails,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _placeOrder(BuildContext context) {
    if (_addressController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a delivery address'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final orderItem = OrderItemEntity(
      itemType: _getItemType(widget.productType),
      itemId: widget.productId,
      quantity: _quantity,
      id: '', // Will be generated by backend
    );

    context.read<OrderBloc>().add(
      CreateOrderEvent(
        customerId: null,
        items: [orderItem],
        deliveryAddress: _addressController.text.trim(),
        paymentMethod: _selectedPaymentMethod,
      ),
    );
  }

  String _getItemType(String productType) {
    switch (productType.toLowerCase()) {
      case 'cylinder':
        return 'CYLINDER';
      case 'package':
        return 'PACKAGE';
      case 'spare_part':
        return 'SPARE_PART';
      default:
        return 'CYLINDER';
    }
  }
}
