import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';

class SupervisorDeliveriesPage extends StatefulWidget {
  const SupervisorDeliveriesPage({super.key});

  @override
  State<SupervisorDeliveriesPage> createState() =>
      _SupervisorDeliveriesPageState();
}

class _SupervisorDeliveriesPageState extends State<SupervisorDeliveriesPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        title: Text(
          'Today\'s Deliveries',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () {
              // TODO: Implement refresh functionality
            },
            icon: Icon(Icons.refresh, color: context.appColors.primaryColor),
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.delivery_dining,
                  size: 80.sp,
                  color: context.appColors.subtextColor,
                ),
                SizedBox(height: 24.h),
                Text(
                  'Supervisor Deliveries',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: context.appColors.textColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 16.h),
                Text(
                  'This page will show today\'s delivery assignments and tracking for supervisors.',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: context.appColors.subtextColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 32.h),
                Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: context.appColors.primaryColor.withValues(
                      alpha: 0.1,
                    ),
                    borderRadius: BorderRadius.circular(12.r),
                    border: Border.all(
                      color: context.appColors.primaryColor.withValues(
                        alpha: 0.3,
                      ),
                    ),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.construction,
                        color: context.appColors.primaryColor,
                        size: 32.sp,
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        'Coming Soon',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              color: context.appColors.primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'Delivery management features will be available soon.',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: context.appColors.primaryColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
