import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:frontend/core/config/theme/colors/app_colors_extension.dart';
import 'package:frontend/core/enums/order_status.dart';
import 'package:frontend/core/utils/extensions/app_bloc_extensions.dart';
import 'package:frontend/core/utils/helpers/snack_bar_helper.dart';
import 'package:frontend/features/order/domain/entities/order_entity.dart';

import '../../../../../core/enums/layout_type.dart';
import '../../../../order/presentation/bloc/order_bloc.dart';
import '../../../../shared/presentation/widgets/custom_list_grid_view.dart';

class SupervisorDeliveriesPage extends StatefulWidget {
  const SupervisorDeliveriesPage({super.key});

  @override
  State<SupervisorDeliveriesPage> createState() =>
      _SupervisorDeliveriesPageState();
}

class _SupervisorDeliveriesPageState extends State<SupervisorDeliveriesPage> {
  void _loadOrdersData({bool forceRefresh = false}) {
    final orderBloc = context.orderBloc;
    final canRefetch = forceRefresh || orderBloc.orders.isEmpty;
    if (!canRefetch) return;

    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = DateTime(
      today.year,
      today.month,
      today.day,
      23,
      59,
      59,
      999,
    );

    orderBloc.add(GetOrdersEvent(startDate: startOfDay, endDate: endOfDay));
  }

  @override
  void initState() {
    super.initState();
    _loadOrdersData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.appColors.backgroundColor,
      appBar: AppBar(
        backgroundColor: context.appColors.surfaceColor,
        elevation: 0,
        title: Text(
          'Today\'s Deliveries',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: context.appColors.textColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () async {
              _loadOrdersData(forceRefresh: true);
            },
            icon: Icon(Icons.refresh, color: context.appColors.primaryColor),
          ),
        ],
      ),
      body: SafeArea(
        child: BlocListener<OrderBloc, OrderState>(
          listener: (context, state) {
            if (state is AssignAgentToOrderSuccess) {
              SnackBarHelper.showSuccessSnackBar(
                context,
                message: state.message,
              );
              // Refresh dashboard data after successful assignment
              _loadOrdersData(forceRefresh: true);
            } else if (state is AssignAgentToOrderFailure) {
              SnackBarHelper.showErrorSnackBar(
                context,
                message: state.appFailure.getErrorMessage(),
              );
            }
          },
          child: BlocBuilder<OrderBloc, OrderState>(
            buildWhen: (previous, current) {
              return current is GetOrdersLoading ||
                  current is GetOrdersSuccess ||
                  current is GetOrdersFailure;
            },
            builder: (context, state) {
              if (state is GetOrdersLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (state is GetOrdersFailure) {
                return _buildErrorWidget(
                  context,
                  state.appFailure.getErrorMessage(),
                );
              }

              // if (state is GetSupervisorDashboardSuccess) {
              final orders = context.orderBloc.orders;
              // final deliveredOrders = orders
              //     .where((order) => order.status == OrderStatus.delivered)
              //     .toList();

              // return _buildOrdersList(context);
              return CustomListGridView<OrderEntity>(
                isEmpty: orders.isEmpty,
                isLoading: state is GetOrdersLoading,
                items: orders,
                layoutType: LayoutType.listView,
                padding: EdgeInsets.all(16.w),
                itemBuilder: (context, order) =>
                    _buildOrderCard(context, order),
                emptyDataBuilder: () =>
                    _buildEmptyWidget(context, 'No orders found'),
                onRefresh: () => _loadOrdersData(forceRefresh: true),
              );
              // }

              // return _buildEmptyWidget(context);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildOrderCard(BuildContext context, OrderEntity order) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Order ID: ${order.id}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: context.appColors.textColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'Customer: ${order.customer.phone}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.textColor,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              'Address: ${order.deliveryAddress}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.textColor,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              'Amount: \$${order.totalAmount.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.textColor,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              'Status: ${order.status.label}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: context.appColors.textColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyWidget(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.delivery_dining,
            size: 64.sp,
            color: context.appColors.subtextColor,
          ),
          SizedBox(height: 16.h),
          Text(
            message,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _loadOrdersData,
            child: const Text('Load Data'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: Colors.red),
          SizedBox(height: 16.h),
          Text(
            'Error Loading Deliveries',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: context.appColors.textColor,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: context.appColors.subtextColor,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: _loadOrdersData,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
}
