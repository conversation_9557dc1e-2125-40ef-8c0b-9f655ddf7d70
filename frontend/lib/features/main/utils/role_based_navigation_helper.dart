import 'package:flutter/material.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';

/// Helper class for role-based navigation and permissions
class RoleBasedNavigationHelper {
  /// Check if user has permission to access a specific feature
  static bool hasPermission(UserRole userRole, String permission) {
    switch (userRole) {
      case UserRole.admin:
        return _adminPermissions.contains(permission);
      case UserRole.agent:
        return _agentPermissions.contains(permission);
      case UserRole.customer:
        return _customerPermissions.contains(permission);
    }
  }

  /// Get role-specific app bar title
  static String getAppBarTitle(UserRole role, int tabIndex) {
    switch (role) {
      case UserRole.customer:
        return _customerTitles[tabIndex] ?? 'Gas Delivery';
      case UserRole.agent:
        return _agentTitles[tabIndex] ?? 'Agent Portal';
      case UserRole.admin:
        return _adminTitles[tabIndex] ?? 'Admin Panel';
    }
  }

  /// Get role-specific greeting message
  static String getGreetingMessage(UserRole role, String? name) {
    final displayName = name ?? _getDefaultName(role);
    switch (role) {
      case UserRole.customer:
        return 'Welcome back, $displayName!';
      case UserRole.agent:
        return 'Ready for deliveries, $displayName?';
      case UserRole.admin:
        return 'Admin Dashboard, $displayName';
    }
  }

  /// Get role-specific primary action
  static String getPrimaryActionText(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return 'Order Gas';
      case UserRole.agent:
        return 'View Deliveries';
      case UserRole.admin:
        return 'Manage System';
    }
  }

  /// Get role-specific primary action icon
  static IconData getPrimaryActionIcon(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return Icons.add_shopping_cart;
      case UserRole.agent:
        return Icons.delivery_dining;
      case UserRole.admin:
        return Icons.dashboard;
    }
  }

  /// Get role-specific color scheme
  static Color getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return Colors.blue;
      case UserRole.agent:
        return Colors.green;
      case UserRole.admin:
        return Colors.purple;
    }
  }

  /// Check if user can access admin features
  static bool canAccessAdminFeatures(UserRole role) {
    return role == UserRole.admin;
  }

  /// Check if user can access agent features
  static bool canAccessAgentFeatures(UserRole role) {
    return role == UserRole.agent || role == UserRole.admin;
  }

  /// Check if user can access customer features
  static bool canAccessCustomerFeatures(UserRole role) {
    return true; // All roles can access customer features
  }

  /// Get role-specific notification settings
  static List<String> getNotificationTypes(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return ['order_updates', 'promotions', 'delivery_status'];
      case UserRole.agent:
        return ['new_orders', 'route_updates', 'earnings', 'system_alerts'];
      case UserRole.admin:
        return ['system_alerts', 'user_reports', 'analytics', 'security'];
    }
  }

  /// Get role-specific menu items
  static List<String> getMenuItems(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return ['Order History', 'Addresses', 'Payment Methods', 'Support'];
      case UserRole.agent:
        return ['Earnings', 'Vehicle Info', 'Performance', 'Support'];
      case UserRole.admin:
        return ['User Management', 'Analytics', 'System Settings', 'Reports'];
    }
  }

  // Private helper methods and constants
  static String _getDefaultName(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return 'Customer';
      case UserRole.agent:
        return 'Agent';
      case UserRole.admin:
        return 'Admin';
    }
  }

  // Permission constants
  static const List<String> _adminPermissions = [
    'manage_users',
    'view_analytics',
    'system_settings',
    'manage_agents',
    'manage_customers',
    'view_reports',
    'manage_orders',
    'manage_payments',
  ];

  static const List<String> _agentPermissions = [
    'view_deliveries',
    'update_delivery_status',
    'view_earnings',
    'update_location',
    'manage_availability',
  ];

  static const List<String> _customerPermissions = [
    'place_order',
    'view_order_history',
    'manage_addresses',
    'manage_payment_methods',
    'track_delivery',
  ];

  // Title constants
  static const Map<int, String> _customerTitles = {
    0: 'Home',
    1: 'My Orders',
    2: 'Profile',
  };

  static const Map<int, String> _agentTitles = {
    0: 'Dashboard',
    1: 'Deliveries',
    2: 'Profile',
  };

  static const Map<int, String> _adminTitles = {
    0: 'Dashboard',
    1: 'User Management',
    2: 'Settings',
  };
}

/// Extension to add role-based functionality to UserEntity
extension UserEntityRoleExtension on UserEntity {
  /// Check if user has a specific permission
  bool hasPermission(String permission) {
    return RoleBasedNavigationHelper.hasPermission(role, permission);
  }

  /// Get user's greeting message
  String getGreetingMessage() {
    return RoleBasedNavigationHelper.getGreetingMessage(role, phone);
  }

  /// Get user's primary action text
  String getPrimaryActionText() {
    return RoleBasedNavigationHelper.getPrimaryActionText(role);
  }

  /// Get user's primary action icon
  IconData getPrimaryActionIcon() {
    return RoleBasedNavigationHelper.getPrimaryActionIcon(role);
  }

  /// Get user's role color
  Color getRoleColor() {
    return RoleBasedNavigationHelper.getRoleColor(role);
  }

  /// Check if user is admin
  bool get isAdmin => role == UserRole.admin;

  /// Check if user is agent
  bool get isAgent => role == UserRole.agent;

  /// Check if user is customer
  bool get isCustomer => role == UserRole.customer;

  /// Get user's notification types
  List<String> getNotificationTypes() {
    return RoleBasedNavigationHelper.getNotificationTypes(role);
  }

  /// Get user's menu items
  List<String> getMenuItems() {
    return RoleBasedNavigationHelper.getMenuItems(role);
  }
}
