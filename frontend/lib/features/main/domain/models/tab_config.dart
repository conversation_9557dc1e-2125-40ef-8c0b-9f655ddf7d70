import 'package:flutter/material.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';
import 'package:frontend/features/main/presentation/pages/customer/customer_home_page.dart';
import 'package:frontend/features/main/presentation/pages/customer/customer_orders_page.dart';
import 'package:frontend/features/main/presentation/pages/customer/customer_profile_page.dart';
import 'package:frontend/features/main/presentation/pages/agent/agent_dashboard_page.dart';
import 'package:frontend/features/main/presentation/pages/admin/admin_dashboard_page.dart';
import 'package:frontend/features/main/presentation/pages/admin/admin_inventory_page.dart';
import 'package:frontend/features/main/presentation/pages/admin/admin_order_management_page.dart';

import '../../../../core/enums/user_role.dart';
import '../../presentation/pages/agent/agent_delivery_page.dart';
import '../../presentation/pages/agent/agent_profile_page.dart';

/// Represents a single tab configuration
class TabConfig {
  final String label;
  final IconData icon;
  final IconData? activeIcon;
  final Widget page;
  final String? badge;

  const TabConfig({
    required this.label,
    required this.icon,
    required this.page,
    this.activeIcon,
    this.badge,
  });
}

/// Role-based tab configurations
class RoleBasedTabConfig {
  static List<TabConfig> getTabsForRole(UserRole role) {
    switch (role) {
      case UserRole.customer:
        return _customerTabs;
      case UserRole.agent:
        return _agentTabs;
      case UserRole.admin:
        return _adminTabs;
      case UserRole.supervisor:
        return _supervisorTabs;
    }
  }

  static List<TabConfig> get _customerTabs => [
    const TabConfig(
      label: 'Home',
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      page: CustomerHomePage(),
    ),
    const TabConfig(
      label: 'Orders',
      icon: Icons.receipt_long_outlined,
      activeIcon: Icons.receipt_long,
      page: CustomerOrdersPage(),
    ),
    const TabConfig(
      label: 'Profile',
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      page: CustomerProfilePage(),
    ),
  ];

  static List<TabConfig> get _agentTabs => [
    const TabConfig(
      label: 'Dashboard',
      icon: Icons.dashboard_outlined,
      activeIcon: Icons.dashboard,
      page: AgentDashboardPage(),
    ),
    const TabConfig(
      label: 'Deliveries',
      icon: Icons.delivery_dining_outlined,
      activeIcon: Icons.delivery_dining,
      page: AgentDeliveriesPage(),
    ),
    const TabConfig(
      label: 'Profile',
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      page: AgentProfilePage(),
    ),
  ];

  static List<TabConfig> get _adminTabs => [
    const TabConfig(
      label: 'Dashboard',
      icon: Icons.dashboard_outlined,
      activeIcon: Icons.dashboard,
      page: AdminDashboardPage(),
    ),
    const TabConfig(
      label: 'Inventory',
      icon: Icons.inventory_outlined,
      activeIcon: Icons.inventory,
      page: AdminInventoryPage(),
    ),
    const TabConfig(
      label: 'Orders',
      icon: Icons.assignment_outlined,
      activeIcon: Icons.assignment,
      page: AdminOrderManagementPage(),
    ),
  ];

  static List<TabConfig> get _supervisorTabs => [
    const TabConfig(
      label: 'Dashboard',
      icon: Icons.dashboard_outlined,
      activeIcon: Icons.dashboard,
      page: SupervisorDashboardPage(),
    ),
    const TabConfig(
      label: 'Deliveries',
      icon: Icons.delivery_dining_outlined,
      activeIcon: Icons.delivery_dining,
      page: SupervisorDeliveriesPage(),
    ),
    const TabConfig(
      label: 'Profile',
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      page: SupervisorProfilePage(),
    ),
  ];

}
