import 'package:equatable/equatable.dart';

enum UserRole {
  customer(value: 'customer'),
  agent(value: 'agent'),
  admin(value: 'admin');

  const UserRole({required this.value});
  final String value;

  static UserRole fromString(String value) {
    return UserRole.values.firstWhere(
      (role) => role.value == value,
      orElse: () => UserRole.customer,
    );
  }
}

enum VehicleType {
  bike('bike'),
  car('car'),
  motorcycle('motorcycle');

  const VehicleType(this.value);
  final String value;

  static VehicleType fromString(String value) {
    return VehicleType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => VehicleType.motorcycle,
    );
  }
}

class LocationEntity extends Equatable {
  final String type;
  final List<double> coordinates; // [longitude, latitude]

  const LocationEntity({required this.type, required this.coordinates});

  @override
  List<Object?> get props => [type, coordinates];
}

class AddressEntity extends Equatable {
  final String? tag;
  final LocationEntity location;
  final String details;
  final String? contactPhone;

  const AddressEntity({
    this.tag,
    required this.location,
    required this.details,
    this.contactPhone,
  });

  @override
  List<Object?> get props => [tag, location, details, contactPhone];
}

class VehicleEntity extends Equatable {
  final VehicleType type;
  final String? number;

  const VehicleEntity({required this.type, this.number});

  @override
  List<Object?> get props => [type, number];
}

class AgentMetadataEntity extends Equatable {
  final VehicleEntity? vehicle;
  final bool isOnDuty;
  final LocationEntity? lastKnownLocation;
  final double? rating;

  const AgentMetadataEntity({
    this.vehicle,
    this.isOnDuty = false,
    this.lastKnownLocation,
    this.rating,
  });

  @override
  List<Object?> get props => [vehicle, isOnDuty, lastKnownLocation, rating];
}

class UserEntity extends Equatable {
  final String id;
  final String phone;
  final String? email;
  final UserRole role;
  final List<AddressEntity> addresses;
  final bool isActive;
  final AgentMetadataEntity? agentMetadata;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const UserEntity({
    required this.id,
    required this.phone,
    this.email,
    required this.role,
    this.addresses = const [],
    this.isActive = true,
    this.agentMetadata,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
    id,
    phone,
    email,
    role,
    addresses,
    isActive,
    agentMetadata,
    createdAt,
    updatedAt,
  ];
}
