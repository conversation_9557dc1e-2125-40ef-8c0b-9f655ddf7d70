import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/errors/app_failure.dart';
import '../../../../core/usecase/usecase.dart';
import '../../../../core/utils/helpers/bloc_helper.dart';
import '../../../authentication/domain/entities/user_entity.dart';
import '../../domain/params/delete_user_params.dart';
import '../../domain/params/get_all_users_params.dart';
import '../../domain/params/register_admin_or_agent_params.dart';
import '../../domain/params/subscribe_notifications_params.dart';
import '../../domain/params/unsubscribe_notifications_params.dart';
import '../../domain/params/update_user_params.dart';
import '../../domain/usecase/delete_user_usecase.dart';
import '../../domain/usecase/get_all_users_usecase.dart';
import '../../domain/usecase/get_current_user_usecase.dart';
import '../../domain/usecase/register_admin_or_agent_usecase.dart';
import '../../domain/usecase/subscribe_to_notifications_usecase.dart';
import '../../domain/usecase/unsubscribe_from_notifications_usecase.dart';
import '../../domain/usecase/update_user_usecase.dart';

part 'user_event.dart';
part 'user_state.dart';

class UserBloc extends Bloc<UserEvent, UserState> {
  final RegisterAdminOrAgentUseCase registerAdminOrAgentUseCase;
  final UpdateUserUseCase updateUserUseCase;
  final DeleteUserUseCase deleteUserUseCase;
  final SubscribeToNotificationsUseCase subscribeToNotificationsUseCase;
  final UnsubscribeFromNotificationsUseCase unsubscribeFromNotificationsUseCase;
  final GetAllUsersUseCase getAllUsersUseCase;
  final GetCurrentUserUseCase getCurrentUserUseCase;

  UserBloc({
    required this.registerAdminOrAgentUseCase,
    required this.updateUserUseCase,
    required this.deleteUserUseCase,
    required this.subscribeToNotificationsUseCase,
    required this.unsubscribeFromNotificationsUseCase,
    required this.getAllUsersUseCase,
    required this.getCurrentUserUseCase,
  }) : super(UserInitial()) {
    /// Register Admin or Agent
    on<RegisterAdminOrAgentEvent>(
      _onRegisterAdminOrAgent,
      transformer: BlocHelper.debounceHelper(),
    );

    /// Update User
    on<UpdateUserEvent>(
      _onUpdateUser,
      transformer: BlocHelper.debounceHelper(),
    );

    /// Delete User
    on<DeleteUserEvent>(
      _onDeleteUser,
      transformer: BlocHelper.debounceHelper(),
    );

    /// Subscribe to Notifications
    on<SubscribeToNotificationsEvent>(
      _onSubscribeToNotifications,
      transformer: BlocHelper.debounceHelper(),
    );

    /// Unsubscribe from Notifications
    on<UnsubscribeFromNotificationsEvent>(
      _onUnsubscribeFromNotifications,
      transformer: BlocHelper.debounceHelper(),
    );

    /// Get All Users
    on<GetAllUsersEvent>(
      _onGetAllUsers,
      transformer: BlocHelper.debounceHelper(),
    );

    /// Get Current User
    on<GetCurrentUserEvent>(
      _onGetCurrentUser,
      transformer: BlocHelper.debounceHelper(),
    );
  }

  UserEntity? _currentUser;
  UserEntity? get currentUser => _currentUser;

  /// 🟢 Handle Register Admin or Agent
  Future<void> _onRegisterAdminOrAgent(
    RegisterAdminOrAgentEvent event,
    Emitter<UserState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, UserState>(
      emit: emit,
      loadingState: RegisterAdminOrAgentLoading(),
      callUseCase: registerAdminOrAgentUseCase(
        params: RegisterAdminOrAgentParams(
          phone: event.phone,
          email: event.email,
          password: event.password,
          role: event.role,
          vehicleType: event.vehicleType,
          vehicleNumber: event.vehicleNumber,
        ),
      ),
      onSuccess: (_) => RegisterAdminOrAgentSuccess(),
      onFailure: (failure) => RegisterAdminOrAgentFailure(appFailure: failure),
    );
  }

  /// 🟢 Handle Update User
  Future<void> _onUpdateUser(
    UpdateUserEvent event,
    Emitter<UserState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, UserState>(
      emit: emit,
      loadingState: UpdateUserLoading(),
      callUseCase: updateUserUseCase(
        params: UpdateUserParams(
          userId: event.userId,
          email: event.email,
          tag: event.tag,
          coordinates: event.coordinates,
          details: event.details,
          contactPhone: event.contactPhone,
        ),
      ),
      onSuccess: (user) =>
          const UpdateUserSuccess(message: 'User updated successfully'),
      onFailure: (failure) => UpdateUserFailure(appFailure: failure),
    );
  }

  /// 🟢 Handle Delete User
  Future<void> _onDeleteUser(
    DeleteUserEvent event,
    Emitter<UserState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, UserState>(
      emit: emit,
      loadingState: DeleteUserLoading(),
      callUseCase: deleteUserUseCase(
        params: DeleteUserParams(userId: event.userId),
      ),
      onSuccess: (_) => DeleteUserSuccess(),
      onFailure: (failure) => DeleteUserFailure(appFailure: failure),
    );
  }

  /// 🟢 Handle Subscribe to Notifications
  Future<void> _onSubscribeToNotifications(
    SubscribeToNotificationsEvent event,
    Emitter<UserState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, UserState>(
      emit: emit,
      loadingState: SubscribeToNotificationsLoading(),
      callUseCase: subscribeToNotificationsUseCase(
        params: SubscribeNotificationsParams(
          userId: event.userId,
          topic: event.topic,
        ),
      ),
      onSuccess: (_) => SubscribeToNotificationsSuccess(),
      onFailure: (failure) =>
          SubscribeToNotificationsFailure(appFailure: failure),
    );
  }

  /// 🟢 Handle Unsubscribe from Notifications
  Future<void> _onUnsubscribeFromNotifications(
    UnsubscribeFromNotificationsEvent event,
    Emitter<UserState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<void, UserState>(
      emit: emit,
      loadingState: UnsubscribeFromNotificationsLoading(),
      callUseCase: unsubscribeFromNotificationsUseCase(
        params: UnsubscribeNotificationsParams(
          userId: event.userId,
          topic: event.topic,
        ),
      ),
      onSuccess: (_) => UnsubscribeFromNotificationsSuccess(),
      onFailure: (failure) =>
          UnsubscribeFromNotificationsFailure(appFailure: failure),
    );
  }

  /// 🟢 Handle Get All Users
  Future<void> _onGetAllUsers(
    GetAllUsersEvent event,
    Emitter<UserState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<List<UserEntity>, UserState>(
      emit: emit,
      loadingState: GetAllUsersLoading(),
      callUseCase: getAllUsersUseCase(
        params: GetAllUsersParams(
          role: event.role,
          isActive: event.isActive,
          page: event.page,
          limit: event.limit,
          sortBy: event.sortBy,
          sortOrder: event.sortOrder,
        ),
      ),
      onSuccess: (users) => GetAllUsersSuccess(users: users),
      onFailure: (failure) => GetAllUsersFailure(appFailure: failure),
    );
  }

  /// 🟢 Handle Get Current User
  Future<void> _onGetCurrentUser(
    GetCurrentUserEvent event,
    Emitter<UserState> emit,
  ) async {
    await BlocHelper.handleEventAndEmit<UserEntity, UserState>(
      emit: emit,
      loadingState: GetCurrentUserLoading(),
      callUseCase: getCurrentUserUseCase(params: NoParams()),
      onSuccess: (user) {
        _currentUser = user;
        return GetCurrentUserSuccess(user: user);
      },
      onFailure: (failure) => GetCurrentUserFailure(appFailure: failure),
    );
  }
}
