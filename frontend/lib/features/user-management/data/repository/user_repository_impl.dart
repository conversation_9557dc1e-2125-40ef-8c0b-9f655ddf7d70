import 'package:fpdart/fpdart.dart';
import 'package:frontend/core/errors/app_failure.dart';

import 'package:frontend/features/authentication/domain/entities/user_entity.dart';
import 'package:frontend/features/user-management/data/source/remote/user_remote_datasource.dart';

import '../../../authentication/data/mappers/authentication_mapper.dart';
import '../../domain/repository/user_repository.dart';

class UserRepositoryImpl implements UserRepository {
  final UserRemoteDatasource userRemoteDatasource;

  const UserRepositoryImpl({required this.userRemoteDatasource});

  @override
  FutureEitherFailOr<void> deleteUser({required String userId}) async {
    final response = await userRemoteDatasource.deleteUser(userId: userId);
    return response;
  }

  @override
  FutureEitherFailOr<List<UserEntity>> getAllUsers({
    UserRole? role,
    bool? isActive,
    int? page,
    int? limit,
    String? sortBy,
    String? sortOrder,
  }) async {
    final response = await userRemoteDatasource.getAllUsers(
      role: role,
      isActive: isActive,
      page: page,
      limit: limit,
      sortBy: sortBy,
      sortOrder: sortOrder,
    );
    return response.fold((failure) => left(failure), (userModelList) {
      final userEntityList = AuthenticationMapper.toUserEntityList(
        userModelList,
      );

      return right(userEntityList);
    });
  }

  @override
  FutureEitherFailOr<UserEntity> getCurrentUser() async {
    final response = await userRemoteDatasource.getCurrentUser();
    return response.fold((failure) => left(failure), (userModel) {
      final userEntity = AuthenticationMapper.toUserEntity(userModel);
      return right(userEntity);
    });
  }

  @override
  FutureEitherFailOr<void> registerAdminOrAgent({
    required String phone,
    String? email,
    required String password,
    required UserRole role,
    required VehicleType vehicleType,
    required String vehicleNumber,
  }) async {
    final response = await userRemoteDatasource.registerAdminOrAgent(
      phone: phone,
      email: email,
      password: password,
      role: role,
      vehicleType: vehicleType,
      vehicleNumber: vehicleNumber,
    );
    return response;
  }

  @override
  FutureEitherFailOr<void> subscribeToNotifications({
    required String userId,
    required String topic,
  }) async {
    // final deviceToken = await FirebaseMessaging.instance.getToken();
    final deviceToken = '';
    final response = await userRemoteDatasource.subscribeToNotifications(
      userId: userId,
      topic: topic,
      deviceToken: deviceToken,
    );
    return response;
  }

  @override
  FutureEitherFailOr<void> unsubscribeFromNotifications({
    required String userId,
    required String topic,
  }) async {
    // final deviceToken = await FirebaseMessaging.instance.getToken();
    final deviceToken = '';
    final response = await userRemoteDatasource.unsubscribeFromNotifications(
      userId: userId,
      topic: topic,
      deviceToken: deviceToken,
    );
    return response;
  }

  @override
  FutureEitherFailOr<void> updateUser({
    required String userId,
    String? email,
    required String? tag,
    required List<double> coordinates,
    required String? details,
    String? contactPhone,
  }) async {
    final response = await userRemoteDatasource.updateUser(
      userId: userId,
      email: email,
      tag: tag,
      coordinates: coordinates,
      details: details,
      contactPhone: contactPhone,
    );
    return response;
  }
}
