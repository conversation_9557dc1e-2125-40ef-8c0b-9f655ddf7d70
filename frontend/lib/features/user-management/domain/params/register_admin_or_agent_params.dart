// register_admin_or_agent_params.dart
import 'package:equatable/equatable.dart';

import '../../../authentication/domain/entities/user_entity.dart';

class RegisterAdminOrAgentParams extends Equatable {
  final String phone;
  final String? email;
  final String password;
  final UserRole role;
  final VehicleType vehicleType;
  final String vehicleNumber;

  const RegisterAdminOrAgentParams({
    required this.phone,
    this.email,
    required this.password,
    required this.role,
    required this.vehicleType,
    required this.vehicleNumber,
  });

  Map<String, dynamic> toJson() {
    return {
      'phone': phone,
      'email': email,
      'role': role.value,
      'vehicleType': vehicleType.value,
      'vehicleNumber': vehicleNumber,
    };
  }

  @override
  List<Object?> get props => [
    phone,
    email,
    password,
    role,
    vehicleType,
    vehicleNumber,
  ];
}
