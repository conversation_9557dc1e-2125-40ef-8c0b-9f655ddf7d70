// get_all_users_params.dart
import 'package:equatable/equatable.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';

class GetAllUsersParams extends Equatable {
  final UserRole? role;
  final bool? isActive;
  final int? page;
  final int? limit;
  final String? sortBy;
  final String? sortOrder;

  const GetAllUsersParams({
    this.role,
    this.isActive,
    this.page,
    this.limit,
    this.sortBy,
    this.sortOrder,
  });

  Map<String, dynamic> toJson() {
    return {
      'role': role?.value,
      'isActive': isActive,
      'page': page,
      'limit': limit,
      'sortBy': sortBy,
      'sortOrder': sortOrder,
    };
  }

  @override
  List<Object?> get props => [role, isActive, page, limit, sortBy, sortOrder];
}