import '../../../../core/errors/app_failure.dart';
import '../../../authentication/domain/entities/user_entity.dart';

abstract class UserRepository {
  FutureEitherFailOr<void> registerAdminOrAgent({
    required String phone,
    String? email,
    required String password,
    required UserRole role,
    required VehicleType vehicleType,
    required String vehicleNumber,
  });

  FutureEitherFailOr<void> updateUser({
    required String userId,
    String? email,
    required String? tag,
    required List<double> coordinates,
    required String? details,
    String? contactPhone,
  });

  FutureEitherFailOr<void> deleteUser({required String userId});

  FutureEitherFailOr<void> subscribeToNotifications({
    required String userId,
    required String topic,
  });

  FutureEitherFailOr<void> unsubscribeFromNotifications({
    required String userId,
    required String topic,
  });

  FutureEitherFailOr<List<UserEntity>> getAllUsers({
    UserRole? role,
    bool? isActive,
    int? page,
    int? limit,
    String? sortBy,
    String? sortOrder,
  });

  FutureEitherFailOr<UserEntity> getCurrentUser();
}
