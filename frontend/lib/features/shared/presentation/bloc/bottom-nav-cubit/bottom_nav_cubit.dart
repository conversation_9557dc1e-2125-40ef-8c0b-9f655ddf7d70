import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:frontend/features/authentication/domain/entities/user_entity.dart';
import 'package:frontend/features/main/domain/models/tab_config.dart';

part 'bottom_nav_state.dart';

class BottomNavCubit extends Cubit<BottomNavState> {
  BottomNavCubit()
    : super(
        const BottomNavState(selectedIndex: 0),
      ); // Default selected index is 0 (Home)

  void changeTab(int index) {
    emit(BottomNavState(selectedIndex: index));
  }

  void resetToHome() {
    emit(const BottomNavState(selectedIndex: 0));
  }

  void updateForRole(UserRole role) {
    // Reset to home when role changes
    emit(const BottomNavState(selectedIndex: 0));
  }

  List<TabConfig> getTabsForRole(UserRole role) {
    return RoleBasedTabConfig.getTabsForRole(role);
  }
}
