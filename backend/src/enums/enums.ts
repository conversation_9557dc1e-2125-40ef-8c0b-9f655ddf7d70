// User Roles
export enum UserRole {
  CUSTOMER = 'customer',
  AGENT = 'agent',
  ADMIN = 'admin',
}

// Order Lifecycle
export enum OrderStatus {
  // Core Flow
  PENDING = 'PENDING', // Order created, awaiting payment
  CONFIRMED = 'CONFIRMED', // Payment successful
  OUT_FOR_DELIVERY = 'OUT_FOR_DELIVERY', // Driver dispatched
  DELIVERED = 'DELIVERED', // Successful completion

  // Termination States
  CANCELLED = 'CANCELLED', // User-initiated cancellation
  FAILED = 'FAILED', // System/payment failures
}
export enum SparePartStatus {
  AVAILABLE = 'AVAILABLE',
  LOW_STOCK = 'LOW_STOCK',
  OUT_OF_STOCK = 'OUT_OF_STOCK',
  DISCONTINUED = 'DISCONTINUED',
}

export enum SparePartCategory {
  VALVE = 'VALVE',
  REGULATOR = 'REGULATOR',
  HOSE = 'HOSE',
  CONNECTOR = 'CONNECTOR',
  GAUGE = 'GAUGE',
  OTHER = 'OTHER',
}

// Cylinder Types
export enum CylinderType {
  SixKg = '6KG',
  ThirteenKg = '13KG',
  SeventeenKg = '17KG',
  TwentyKg = '20KG',
  TwentyFiveKg = '25KG',
}

export enum CylinderStatus {
  Active = 'ACTIVE',
  Discontinued = 'DISCONTINUED',
  OutOfStock = 'OUT_OF_STOCK',
}

export enum CylinderMaterial {
  Iron = 'IRON',
  Plastic = 'PLASTIC',
}

export enum VehicleType {
  BIKE = 'bike',
  CAR = 'car',
  MOTORCYCLE = 'motorcycle',
}

// export enum Warehouse { // instead of `warehouse`
//   MAIN = 'ceelasha_warehouse',
//   SECONDARY = 'seybiyaano_warehouse',
//   THIRD = 'towfiiq_warehouse',
//   FOURTH = 'bakaaro_warehouse',
// }

// Payment Methods
export enum PaymentMethod {
  CASH = 'cash',
  WAAFI_PREAUTH = 'waafi_preauth',
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  PREAUTHORIZED = 'PREAUTHORIZED',
  CAPTURED = 'CAPTURED',
  CANCELLED = 'CANCELLED',
  FAILED = 'FAILED',
}

// Delivery Verification
export enum DeliveryVerificationMethod {
  QR_CODE = 'qr_code',
  OTP = 'otp',
  BIOMETRIC = 'biometric', // Future-proofing
}

export enum NotificationStatus {
  PENDING = 'pending',
  DELIVERED = 'delivered',
  FAILED = 'failed',
}
