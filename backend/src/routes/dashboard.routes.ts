import { Router } from 'express';
import { dashboardController } from '../controllers/dashboard.controller';
import { authenticate } from '../middleware/auth.middleware';
import { validateRole } from '../middleware/role.middleware';
import { UserRole } from '../enums/enums';

const dashboardRouter = Router();

/**
 * Dashboard Routes
 * All routes require authentication
 */

// General dashboard summary for current user (based on their role)
dashboardRouter.get('/summary', authenticate, dashboardController.getDashboardSummary);

// Admin dashboard routes
dashboardRouter.get(
  '/admin',
  authenticate,
  validateRole([UserRole.ADMIN]),
  dashboardController.getAdminDashboard
);

// Admin reports endpoint
dashboardRouter.get(
  '/reports',
  authenticate,
  validateRole([UserRole.ADMIN]),
  dashboardController.getDashboardReports
);

// Agent dashboard routes
// Agents can access their own dashboard, admins can access any agent's dashboard
dashboardRouter.get(
  '/agent/:agentId',
  authenticate,
  validateRole([UserRole.ADMIN, UserRole.AGENT]),
  dashboardController.getAgentDashboard
);

// Agent dashboard for current user (if agent)
dashboardRouter.get(
  '/agent',
  authenticate,
  validateRole([UserRole.AGENT]),
  dashboardController.getAgentDashboard
);

// User/Customer dashboard routes
// Customers can access their own dashboard, admins can access any user's dashboard
dashboardRouter.get(
  '/user/:userId',
  authenticate,
  validateRole([UserRole.ADMIN, UserRole.CUSTOMER]),
  dashboardController.getUserDashboard
);

// User dashboard for current user (if customer)
dashboardRouter.get(
  '/customer',
  authenticate,
  validateRole([UserRole.CUSTOMER]),
  dashboardController.getUserDashboard
);

export default dashboardRouter;
